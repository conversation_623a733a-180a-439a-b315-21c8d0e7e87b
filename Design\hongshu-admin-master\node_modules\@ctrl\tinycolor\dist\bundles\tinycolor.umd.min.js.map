{"version": 3, "file": "tinycolor.umd.min.js", "sources": ["../module/css-color-names.js", "../module/util.js", "../module/conversion.js", "../module/format-input.js", "../module/index.js", "../module/random.js", "../module/readability.js", "../module/umd_api.js", "../module/from-ratio.js", "../module/to-ms-filter.js"], "sourcesContent": ["// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexport var names = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    goldenrod: '#daa520',\n    gold: '#ffd700',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavenderblush: '#fff0f5',\n    lavender: '#e6e6fa',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32',\n};\n", "/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = '100%';\n    }\n    var isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? (n % max) + max : n % max) / parseFloat(String(max));\n    }\n    else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = (n % max) / parseFloat(String(max));\n    }\n    return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n    return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n    return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n    if (n <= 1) {\n        return \"\".concat(Number(n) * 100, \"%\");\n    }\n    return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n    return c.length === 1 ? '0' + c : String(c);\n}\n", "import { bound01, pad2 } from './util.js';\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nexport function rgbToRgb(r, g, b) {\n    return {\n        r: bound01(r, 255) * 255,\n        g: bound01(g, 255) * 255,\n        b: bound01(b, 255) * 255,\n    };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nexport function rgbToHsl(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var s = 0;\n    var l = (max + min) / 2;\n    if (max === min) {\n        s = 0;\n        h = 0; // achromatic\n    }\n    else {\n        var d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, l: l };\n}\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * (6 * t);\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hslToRgb(h, s, l) {\n    var r;\n    var g;\n    var b;\n    h = bound01(h, 360);\n    s = bound01(s, 100);\n    l = bound01(l, 100);\n    if (s === 0) {\n        // achromatic\n        g = l;\n        b = l;\n        r = l;\n    }\n    else {\n        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n        var p = 2 * l - q;\n        r = hue2rgb(p, q, h + 1 / 3);\n        g = hue2rgb(p, q, h);\n        b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nexport function rgbToHsv(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var v = max;\n    var d = max - min;\n    var s = max === 0 ? 0 : d / max;\n    if (max === min) {\n        h = 0; // achromatic\n    }\n    else {\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, v: v };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hsvToRgb(h, s, v) {\n    h = bound01(h, 360) * 6;\n    s = bound01(s, 100);\n    v = bound01(v, 100);\n    var i = Math.floor(h);\n    var f = h - i;\n    var p = v * (1 - s);\n    var q = v * (1 - f * s);\n    var t = v * (1 - (1 - f) * s);\n    var mod = i % 6;\n    var r = [v, q, p, p, t, v][mod];\n    var g = [t, v, v, q, p, p][mod];\n    var b = [p, p, t, v, v, q][mod];\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color to hex\n *\n * Assumes r, g, and b are contained in the set [0, 255]\n * Returns a 3 or 6 character hex\n */\nexport function rgbToHex(r, g, b, allow3Char) {\n    var hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    // Return a 3 character hex if possible\n    if (allow3Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * Assumes r, g, b are contained in the set [0, 255] and\n * a in [0, 1]. Returns a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nexport function rgbaToHex(r, g, b, a, allow4Char) {\n    var hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n        pad2(convertDecimalToHex(a)),\n    ];\n    // Return a 4 character hex if possible\n    if (allow4Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1)) &&\n        hex[3].startsWith(hex[3].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n */\nexport function rgbaToArgbHex(r, g, b, a) {\n    var hex = [\n        pad2(convertDecimalToHex(a)),\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    return hex.join('');\n}\n/** Converts a decimal to a hex value */\nexport function convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */\nexport function convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */\nexport function parseIntFromHex(val) {\n    return parseInt(val, 16);\n}\nexport function numberInputToObject(color) {\n    return {\n        r: color >> 16,\n        g: (color & 0xff00) >> 8,\n        b: color & 0xff,\n    };\n}\n", "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\nimport { convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nexport function inputToRGB(color) {\n    var rgb = { r: 0, g: 0, b: 0 };\n    var a = 1;\n    var s = null;\n    var v = null;\n    var l = null;\n    var ok = false;\n    var format = false;\n    if (typeof color === 'string') {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === 'object') {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = rgbToRgb(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = convertToPercentage(color.s);\n            v = convertToPercentage(color.v);\n            rgb = hsvToRgb(color.h, s, v);\n            ok = true;\n            format = 'hsv';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = convertToPercentage(color.s);\n            l = convertToPercentage(color.l);\n            rgb = hslToRgb(color.h, s, l);\n            ok = true;\n            format = 'hsl';\n        }\n        if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n            a = color.a;\n        }\n    }\n    a = boundAlpha(a);\n    return {\n        ok: ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a: a,\n    };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n    rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n    hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n    hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n    hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n    hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nexport function stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    var named = false;\n    if (names[color]) {\n        color = names[color];\n        named = true;\n    }\n    else if (color === 'transparent') {\n        return { r: 0, g: 0, b: 0, a: 0, format: 'name' };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    var match = matchers.rgb.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3] };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3], a: match[4] };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3] };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3], a: match[4] };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3] };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3], a: match[4] };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            a: convertHexToDecimal(match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            a: convertHexToDecimal(match[4] + match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n    return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}\n", "import { numberInputToObject, rgbaToHex, rgbToHex, rgbToHsl, rgbToHsv } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nvar TinyColor = /** @class */ (function () {\n    function TinyColor(color, opts) {\n        if (color === void 0) { color = ''; }\n        if (opts === void 0) { opts = {}; }\n        var _a;\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = numberInputToObject(color);\n        }\n        this.originalInput = color;\n        var rgb = inputToRGB(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    TinyColor.prototype.isDark = function () {\n        return this.getBrightness() < 128;\n    };\n    TinyColor.prototype.isLight = function () {\n        return !this.isDark();\n    };\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    TinyColor.prototype.getBrightness = function () {\n        // http://www.w3.org/TR/AERT#color-contrast\n        var rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    };\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    TinyColor.prototype.getLuminance = function () {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        var rgb = this.toRgb();\n        var R;\n        var G;\n        var B;\n        var RsRGB = rgb.r / 255;\n        var GsRGB = rgb.g / 255;\n        var BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    };\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    TinyColor.prototype.getAlpha = function () {\n        return this.a;\n    };\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    TinyColor.prototype.setAlpha = function (alpha) {\n        this.a = boundAlpha(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    };\n    /**\n     * Returns whether the color is monochrome.\n     */\n    TinyColor.prototype.isMonochrome = function () {\n        var s = this.toHsl().s;\n        return s === 0;\n    };\n    /**\n     * Returns the object as a HSVA object.\n     */\n    TinyColor.prototype.toHsv = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    };\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHsvString = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        var h = Math.round(hsv.h * 360);\n        var s = Math.round(hsv.s * 100);\n        var v = Math.round(hsv.v * 100);\n        return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a HSLA object.\n     */\n    TinyColor.prototype.toHsl = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    };\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHslString = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        var h = Math.round(hsl.h * 360);\n        var s = Math.round(hsl.s * 100);\n        var l = Math.round(hsl.l * 100);\n        return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHex = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return rgbToHex(this.r, this.g, this.b, allow3Char);\n    };\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHexString = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return '#' + this.toHex(allow3Char);\n    };\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8 = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n    };\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8String = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return '#' + this.toHex8(allow4Char);\n    };\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    TinyColor.prototype.toHexShortString = function (allowShortChar) {\n        if (allowShortChar === void 0) { allowShortChar = false; }\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toRgb = function () {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toRgbString = function () {\n        var r = Math.round(this.r);\n        var g = Math.round(this.g);\n        var b = Math.round(this.b);\n        return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toPercentageRgb = function () {\n        var fmt = function (x) { return \"\".concat(Math.round(bound01(x, 255) * 100), \"%\"); };\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    TinyColor.prototype.toPercentageRgbString = function () {\n        var rnd = function (x) { return Math.round(bound01(x, 255) * 100); };\n        return this.a === 1\n            ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\")\n            : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    TinyColor.prototype.toName = function () {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        var hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n        for (var _i = 0, _a = Object.entries(names); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    };\n    TinyColor.prototype.toString = function (format) {\n        var formatSet = Boolean(format);\n        format = format !== null && format !== void 0 ? format : this.format;\n        var formattedString = false;\n        var hasAlpha = this.a < 1 && this.a >= 0;\n        var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        return formattedString || this.toHexString();\n    };\n    TinyColor.prototype.toNumber = function () {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    };\n    TinyColor.prototype.clone = function () {\n        return new TinyColor(this.toString());\n    };\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.lighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.brighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    };\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.darken = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.tint = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('white', amount);\n    };\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.shade = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('black', amount);\n    };\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.desaturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.saturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    TinyColor.prototype.greyscale = function () {\n        return this.desaturate(100);\n    };\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    TinyColor.prototype.spin = function (amount) {\n        var hsl = this.toHsl();\n        var hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    TinyColor.prototype.mix = function (color, amount) {\n        if (amount === void 0) { amount = 50; }\n        var rgb1 = this.toRgb();\n        var rgb2 = new TinyColor(color).toRgb();\n        var p = amount / 100;\n        var rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    };\n    TinyColor.prototype.analogous = function (results, slices) {\n        if (results === void 0) { results = 6; }\n        if (slices === void 0) { slices = 30; }\n        var hsl = this.toHsl();\n        var part = 360 / slices;\n        var ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    };\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    TinyColor.prototype.complement = function () {\n        var hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    };\n    TinyColor.prototype.monochromatic = function (results) {\n        if (results === void 0) { results = 6; }\n        var hsv = this.toHsv();\n        var h = hsv.h;\n        var s = hsv.s;\n        var v = hsv.v;\n        var res = [];\n        var modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h: h, s: s, v: v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    };\n    TinyColor.prototype.splitcomplement = function () {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    };\n    /**\n     * Compute how the color would appear on a background\n     */\n    TinyColor.prototype.onBackground = function (background) {\n        var fg = this.toRgb();\n        var bg = new TinyColor(background).toRgb();\n        var alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    };\n    /**\n     * Alias for `polyad(3)`\n     */\n    TinyColor.prototype.triad = function () {\n        return this.polyad(3);\n    };\n    /**\n     * Alias for `polyad(4)`\n     */\n    TinyColor.prototype.tetrad = function () {\n        return this.polyad(4);\n    };\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    TinyColor.prototype.polyad = function (n) {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        var result = [this];\n        var increment = 360 / n;\n        for (var i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    };\n    /**\n     * compare color vs current color\n     */\n    TinyColor.prototype.equals = function (color) {\n        return this.toRgbString() === new TinyColor(color).toRgbString();\n    };\n    return TinyColor;\n}());\nexport { TinyColor };\n// kept for backwards compatability with v1\nexport function tinycolor(color, opts) {\n    if (color === void 0) { color = ''; }\n    if (opts === void 0) { opts = {}; }\n    return new TinyColor(color, opts);\n}\n", "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\n// randomColor by <PERSON> under the CC0 license\n// https://github.com/davidmerfield/randomColor/\nimport { TinyColor } from './index.js';\nexport function random(options) {\n    if (options === void 0) { options = {}; }\n    // Check if we need to generate multiple colors\n    if (options.count !== undefined &&\n        options.count !== null) {\n        var totalColors = options.count;\n        var colors = [];\n        options.count = undefined;\n        while (totalColors > colors.length) {\n            // Since we're generating multiple colors,\n            // incremement the seed. Otherwise we'd just\n            // generate the same color each time...\n            options.count = null;\n            if (options.seed) {\n                options.seed += 1;\n            }\n            colors.push(random(options));\n        }\n        options.count = totalColors;\n        return colors;\n    }\n    // First we pick a hue (H)\n    var h = pickHue(options.hue, options.seed);\n    // Then use H to determine saturation (S)\n    var s = pickSaturation(h, options);\n    // Then use S and H to determine brightness (B).\n    var v = pickBrightness(h, s, options);\n    var res = { h: h, s: s, v: v };\n    if (options.alpha !== undefined) {\n        res.a = options.alpha;\n    }\n    // Then we return the HSB color in the desired format\n    return new TinyColor(res);\n}\nfunction pickHue(hue, seed) {\n    var hueRange = getHueRange(hue);\n    var res = randomWithin(hueRange, seed);\n    // Instead of storing red as two seperate ranges,\n    // we group them, using negative numbers\n    if (res < 0) {\n        res = 360 + res;\n    }\n    return res;\n}\nfunction pickSaturation(hue, options) {\n    if (options.hue === 'monochrome') {\n        return 0;\n    }\n    if (options.luminosity === 'random') {\n        return randomWithin([0, 100], options.seed);\n    }\n    var saturationRange = getColorInfo(hue).saturationRange;\n    var sMin = saturationRange[0];\n    var sMax = saturationRange[1];\n    switch (options.luminosity) {\n        case 'bright':\n            sMin = 55;\n            break;\n        case 'dark':\n            sMin = sMax - 10;\n            break;\n        case 'light':\n            sMax = 55;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([sMin, sMax], options.seed);\n}\nfunction pickBrightness(H, S, options) {\n    var bMin = getMinimumBrightness(H, S);\n    var bMax = 100;\n    switch (options.luminosity) {\n        case 'dark':\n            bMax = bMin + 20;\n            break;\n        case 'light':\n            bMin = (bMax + bMin) / 2;\n            break;\n        case 'random':\n            bMin = 0;\n            bMax = 100;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([bMin, bMax], options.seed);\n}\nfunction getMinimumBrightness(H, S) {\n    var lowerBounds = getColorInfo(H).lowerBounds;\n    for (var i = 0; i < lowerBounds.length - 1; i++) {\n        var s1 = lowerBounds[i][0];\n        var v1 = lowerBounds[i][1];\n        var s2 = lowerBounds[i + 1][0];\n        var v2 = lowerBounds[i + 1][1];\n        if (S >= s1 && S <= s2) {\n            var m = (v2 - v1) / (s2 - s1);\n            var b = v1 - m * s1;\n            return m * S + b;\n        }\n    }\n    return 0;\n}\nfunction getHueRange(colorInput) {\n    var num = parseInt(colorInput, 10);\n    if (!Number.isNaN(num) && num < 360 && num > 0) {\n        return [num, num];\n    }\n    if (typeof colorInput === 'string') {\n        var namedColor = bounds.find(function (n) { return n.name === colorInput; });\n        if (namedColor) {\n            var color = defineColor(namedColor);\n            if (color.hueRange) {\n                return color.hueRange;\n            }\n        }\n        var parsed = new TinyColor(colorInput);\n        if (parsed.isValid) {\n            var hue = parsed.toHsv().h;\n            return [hue, hue];\n        }\n    }\n    return [0, 360];\n}\nfunction getColorInfo(hue) {\n    // Maps red colors to make picking hue easier\n    if (hue >= 334 && hue <= 360) {\n        hue -= 360;\n    }\n    for (var _i = 0, bounds_1 = bounds; _i < bounds_1.length; _i++) {\n        var bound = bounds_1[_i];\n        var color = defineColor(bound);\n        if (color.hueRange && hue >= color.hueRange[0] && hue <= color.hueRange[1]) {\n            return color;\n        }\n    }\n    throw Error('Color not found');\n}\nfunction randomWithin(range, seed) {\n    if (seed === undefined) {\n        return Math.floor(range[0] + Math.random() * (range[1] + 1 - range[0]));\n    }\n    // Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n    var max = range[1] || 1;\n    var min = range[0] || 0;\n    seed = (seed * 9301 + 49297) % 233280;\n    var rnd = seed / 233280.0;\n    return Math.floor(min + rnd * (max - min));\n}\nfunction defineColor(bound) {\n    var sMin = bound.lowerBounds[0][0];\n    var sMax = bound.lowerBounds[bound.lowerBounds.length - 1][0];\n    var bMin = bound.lowerBounds[bound.lowerBounds.length - 1][1];\n    var bMax = bound.lowerBounds[0][1];\n    return {\n        name: bound.name,\n        hueRange: bound.hueRange,\n        lowerBounds: bound.lowerBounds,\n        saturationRange: [sMin, sMax],\n        brightnessRange: [bMin, bMax],\n    };\n}\n/**\n * @hidden\n */\nexport var bounds = [\n    {\n        name: 'monochrome',\n        hueRange: null,\n        lowerBounds: [\n            [0, 0],\n            [100, 0],\n        ],\n    },\n    {\n        name: 'red',\n        hueRange: [-26, 18],\n        lowerBounds: [\n            [20, 100],\n            [30, 92],\n            [40, 89],\n            [50, 85],\n            [60, 78],\n            [70, 70],\n            [80, 60],\n            [90, 55],\n            [100, 50],\n        ],\n    },\n    {\n        name: 'orange',\n        hueRange: [19, 46],\n        lowerBounds: [\n            [20, 100],\n            [30, 93],\n            [40, 88],\n            [50, 86],\n            [60, 85],\n            [70, 70],\n            [100, 70],\n        ],\n    },\n    {\n        name: 'yellow',\n        hueRange: [47, 62],\n        lowerBounds: [\n            [25, 100],\n            [40, 94],\n            [50, 89],\n            [60, 86],\n            [70, 84],\n            [80, 82],\n            [90, 80],\n            [100, 75],\n        ],\n    },\n    {\n        name: 'green',\n        hueRange: [63, 178],\n        lowerBounds: [\n            [30, 100],\n            [40, 90],\n            [50, 85],\n            [60, 81],\n            [70, 74],\n            [80, 64],\n            [90, 50],\n            [100, 40],\n        ],\n    },\n    {\n        name: 'blue',\n        hueRange: [179, 257],\n        lowerBounds: [\n            [20, 100],\n            [30, 86],\n            [40, 80],\n            [50, 74],\n            [60, 60],\n            [70, 52],\n            [80, 44],\n            [90, 39],\n            [100, 35],\n        ],\n    },\n    {\n        name: 'purple',\n        hueRange: [258, 282],\n        lowerBounds: [\n            [20, 100],\n            [30, 87],\n            [40, 79],\n            [50, 70],\n            [60, 65],\n            [70, 59],\n            [80, 52],\n            [90, 45],\n            [100, 42],\n        ],\n    },\n    {\n        name: 'pink',\n        hueRange: [283, 334],\n        lowerBounds: [\n            [20, 100],\n            [30, 90],\n            [40, 86],\n            [60, 84],\n            [80, 80],\n            [90, 75],\n            [100, 73],\n        ],\n    },\n];\n", "import { TinyColor } from './index.js';\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nexport function readability(color1, color2) {\n    var c1 = new TinyColor(color1);\n    var c2 = new TinyColor(color2);\n    return ((Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) /\n        (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05));\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nexport function isReadable(color1, color2, wcag2) {\n    var _a, _b;\n    if (wcag2 === void 0) { wcag2 = { level: 'AA', size: 'small' }; }\n    var readabilityLevel = readability(color1, color2);\n    switch (((_a = wcag2.level) !== null && _a !== void 0 ? _a : 'AA') + ((_b = wcag2.size) !== null && _b !== void 0 ? _b : 'small')) {\n        case 'AAsmall':\n        case 'AAAlarge':\n            return readabilityLevel >= 4.5;\n        case 'AAlarge':\n            return readabilityLevel >= 3;\n        case 'AAAsmall':\n            return readabilityLevel >= 7;\n        default:\n            return false;\n    }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nexport function mostReadable(baseColor, colorList, args) {\n    if (args === void 0) { args = { includeFallbackColors: false, level: 'AA', size: 'small' }; }\n    var bestColor = null;\n    var bestScore = 0;\n    var includeFallbackColors = args.includeFallbackColors, level = args.level, size = args.size;\n    for (var _i = 0, colorList_1 = colorList; _i < colorList_1.length; _i++) {\n        var color = colorList_1[_i];\n        var score = readability(baseColor, color);\n        if (score > bestScore) {\n            bestScore = score;\n            bestColor = new TinyColor(color);\n        }\n    }\n    if (isReadable(baseColor, bestColor, { level: level, size: size }) || !includeFallbackColors) {\n        return bestColor;\n    }\n    args.includeFallbackColors = false;\n    return mostReadable(baseColor, ['#fff', '#000'], args);\n}\n", "import { names } from './css-color-names.js';\nimport { inputToRGB, isValidCSSUnit, stringInputToObject } from './format-input.js';\nimport { fromRatio, legacyRandom } from './from-ratio.js';\nimport { TinyColor, tinycolor } from './index.js';\nimport { random } from './random.js';\nimport { mostReadable, readability } from './readability.js';\nimport { toMsFilter } from './to-ms-filter.js';\nvar tinycolorumd = tinycolor;\ntinycolorumd.TinyColor = TinyColor;\ntinycolorumd.readability = readability;\ntinycolorumd.mostReadable = mostReadable;\ntinycolorumd.random = random;\ntinycolorumd.names = names;\ntinycolorumd.fromRatio = fromRatio;\ntinycolorumd.legacyRandom = legacyRandom;\ntinycolorumd.toMsFilter = toMsFilter;\ntinycolorumd.inputToRGB = inputToRGB;\ntinycolorumd.stringInputToObject = stringInputToObject;\ntinycolorumd.isValidCSSUnit = isValidCSSUnit;\nexport default tinycolorumd;\n", "import { TinyColor } from './index.js';\nimport { convertToPercentage } from './util.js';\n/**\n * If input is an object, force 1 into \"1.0\" to handle ratios properly\n * String input requires \"1.0\" as input, so 1 will be treated as 1\n */\nexport function fromRatio(ratio, opts) {\n    var newColor = {\n        r: convertToPercentage(ratio.r),\n        g: convertToPercentage(ratio.g),\n        b: convertToPercentage(ratio.b),\n    };\n    if (ratio.a !== undefined) {\n        newColor.a = Number(ratio.a);\n    }\n    return new TinyColor(newColor, opts);\n}\n/** old random function */\nexport function legacyRandom() {\n    return new TinyColor({\n        r: Math.random(),\n        g: Math.random(),\n        b: Math.random(),\n    });\n}\n", "import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n    var color = new TinyColor(firstColor);\n    var hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n    var secondHex8String = hex8String;\n    var gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n    if (secondColor) {\n        var s = new TinyColor(secondColor);\n        secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\".concat(gradientType, \"startColorstr=\").concat(hex8String, \",endColorstr=\").concat(secondHex8String, \")\");\n}\n"], "names": ["names", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "goldenrod", "gold", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavenderblush", "lavender", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "bound01", "n", "max", "indexOf", "parseFloat", "isOnePointZero", "isPercent", "isPercentage", "Math", "min", "parseInt", "String", "abs", "clamp01", "val", "boundAlpha", "a", "isNaN", "convertToPercentage", "concat", "Number", "pad2", "c", "length", "rgbToHsl", "r", "g", "b", "h", "s", "l", "d", "hue2rgb", "p", "q", "t", "rgbToHsv", "v", "rgbToHex", "allow3Char", "hex", "round", "toString", "startsWith", "char<PERSON>t", "join", "rgbaToArgbHex", "convertDecimalToHex", "convertHexToDecimal", "parseIntFromHex", "inputToRGB", "color", "rgb", "ok", "format", "stringInputToObject", "isValidCSSUnit", "substr", "i", "floor", "f", "mod", "hsvToRgb", "hslToRgb", "Object", "prototype", "hasOwnProperty", "call", "CSS_UNIT", "PERMISSIVE_MATCH3", "PERMISSIVE_MATCH4", "matchers", "RegExp", "rgba", "hsl", "hsla", "hsv", "hsva", "hex3", "hex6", "hex4", "hex8", "trim", "toLowerCase", "named", "match", "exec", "Boolean", "TinyColor", "opts", "_a", "numberInputToObject", "this", "originalInput", "roundA", "gradientType", "<PERSON><PERSON><PERSON><PERSON>", "isDark", "getBrightness", "isLight", "toRgb", "getLuminance", "RsRGB", "GsRGB", "BsRGB", "pow", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "isMonochrome", "toHsl", "toHsv", "toHsvString", "toHslString", "toHex", "toHexString", "toHex8", "allow4Char", "rgbaToHex", "toHex8String", "toHexShortString", "allowShortChar", "toRgbString", "toPercentageRgb", "fmt", "x", "toPercentageRgbString", "rnd", "to<PERSON>ame", "_i", "entries", "_b", "key", "formatSet", "formattedString", "has<PERSON><PERSON><PERSON>", "toNumber", "clone", "lighten", "amount", "brighten", "darken", "tint", "mix", "shade", "desaturate", "saturate", "greyscale", "spin", "hue", "rgb1", "rgb2", "analogous", "results", "slices", "part", "ret", "push", "complement", "monochromatic", "res", "modification", "splitcomplement", "onBackground", "background", "fg", "bg", "triad", "polyad", "tetrad", "result", "increment", "equals", "getColorInfo", "bounds_1", "bounds", "defineColor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "randomWithin", "range", "seed", "undefined", "random", "bound", "sMin", "lowerBounds", "sMax", "bMin", "bMax", "name", "saturationRange", "brightnessRange", "readability", "color1", "color2", "c1", "c2", "tinycolorumd", "mostReadable", "baseColor", "colorList", "args", "includeFallbackColors", "level", "size", "bestColor", "bestScore", "colorList_1", "score", "wcag2", "readabilityLevel", "isReadable", "options", "count", "totalColors", "colors", "colorInput", "num", "namedColor", "find", "parsed", "getHueRange", "pickHue", "luminosity", "pickSaturation", "H", "S", "s1", "v1", "s2", "v2", "m", "getMinimumBrightness", "pickBrightness", "fromRatio", "ratio", "newColor", "legacyRandom", "to<PERSON><PERSON><PERSON><PERSON>", "firstColor", "secondColor", "hex8String", "secondHex8String"], "mappings": "0OAIO,IAAIA,EAAQ,CACfC,UAAW,UACXC,aAAc,UACdC,KAAM,UACNC,WAAY,UACZC,MAAO,UACPC,MAAO,UACPC,OAAQ,UACRC,MAAO,UACPC,eAAgB,UAChBC,KAAM,UACNC,WAAY,UACZC,MAAO,UACPC,UAAW,UACXC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,MAAO,UACPC,eAAgB,UAChBC,SAAU,UACVC,QAAS,UACTC,KAAM,UACNC,SAAU,UACVC,SAAU,UACVC,cAAe,UACfC,SAAU,UACVC,UAAW,UACXC,SAAU,UACVC,UAAW,UACXC,YAAa,UACbC,eAAgB,UAChBC,WAAY,UACZC,WAAY,UACZC,QAAS,UACTC,WAAY,UACZC,aAAc,UACdC,cAAe,UACfC,cAAe,UACfC,cAAe,UACfC,cAAe,UACfC,WAAY,UACZC,SAAU,UACVC,YAAa,UACbC,QAAS,UACTC,QAAS,UACTC,WAAY,UACZC,UAAW,UACXC,YAAa,UACbC,YAAa,UACbC,QAAS,UACTC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,MAAO,UACPC,YAAa,UACbC,KAAM,UACNC,SAAU,UACVC,QAAS,UACTC,UAAW,UACXC,OAAQ,UACRC,MAAO,UACPC,MAAO,UACPC,cAAe,UACfC,SAAU,UACVC,UAAW,UACXC,aAAc,UACdC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,qBAAsB,UACtBC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,UAAW,UACXC,YAAa,UACbC,cAAe,UACfC,aAAc,UACdC,eAAgB,UAChBC,eAAgB,UAChBC,eAAgB,UAChBC,YAAa,UACbC,KAAM,UACNC,UAAW,UACXC,MAAO,UACPC,QAAS,UACTC,OAAQ,UACRC,iBAAkB,UAClBC,WAAY,UACZC,aAAc,UACdC,aAAc,UACdC,eAAgB,UAChBC,gBAAiB,UACjBC,kBAAmB,UACnBC,gBAAiB,UACjBC,gBAAiB,UACjBC,aAAc,UACdC,UAAW,UACXC,UAAW,UACXC,SAAU,UACVC,YAAa,UACbC,KAAM,UACNC,QAAS,UACTC,MAAO,UACPC,UAAW,UACXC,OAAQ,UACRC,UAAW,UACXC,OAAQ,UACRC,cAAe,UACfC,UAAW,UACXC,cAAe,UACfC,cAAe,UACfC,WAAY,UACZC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,KAAM,UACNC,WAAY,UACZC,OAAQ,UACRC,cAAe,UACfC,IAAK,UACLC,UAAW,UACXC,UAAW,UACXC,YAAa,UACbC,OAAQ,UACRC,WAAY,UACZC,SAAU,UACVC,SAAU,UACVC,OAAQ,UACRC,OAAQ,UACRC,QAAS,UACTC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,KAAM,UACNC,YAAa,UACbC,UAAW,UACXC,IAAK,UACLC,KAAM,UACNC,QAAS,UACTC,OAAQ,UACRC,UAAW,UACXC,OAAQ,UACRC,MAAO,UACPC,MAAO,UACPC,WAAY,UACZC,OAAQ,UACRC,YAAa,WCpJV,SAASC,EAAQC,EAAGC,IAwCpB,SAAwBD,GAC3B,MAAoB,iBAANA,IAAsC,IAApBA,EAAEE,QAAQ,MAAiC,IAAlBC,WAAWH,EACxE,EAzCQI,CAAeJ,KACfA,EAAI,QAER,IAAIK,EA2CD,SAAsBL,GACzB,MAAoB,iBAANA,IAAsC,IAApBA,EAAEE,QAAQ,IAC9C,CA7CoBI,CAAaN,GAO7B,OANAA,EAAY,MAARC,EAAcD,EAAIO,KAAKC,IAAIP,EAAKM,KAAKN,IAAI,EAAGE,WAAWH,KAEvDK,IACAL,EAAIS,SAASC,OAAOV,EAAIC,GAAM,IAAM,KAGpCM,KAAKI,IAAIX,EAAIC,GAAO,KACb,EAOPD,EAJQ,MAARC,GAIKD,EAAI,EAAKA,EAAIC,EAAOA,EAAMD,EAAIC,GAAOE,WAAWO,OAAOT,IAKvDD,EAAIC,EAAOE,WAAWO,OAAOT,GAG1C,CAKO,SAASW,EAAQC,GACpB,OAAON,KAAKC,IAAI,EAAGD,KAAKN,IAAI,EAAGY,GACnC,CAoBO,SAASC,EAAWC,GAKvB,OAJAA,EAAIZ,WAAWY,IACXC,MAAMD,IAAMA,EAAI,GAAKA,EAAI,KACzBA,EAAI,GAEDA,CACX,CAKO,SAASE,EAAoBjB,GAChC,OAAIA,GAAK,EACE,GAAGkB,OAAmB,IAAZC,OAAOnB,GAAU,KAE/BA,CACX,CAKO,SAASoB,EAAKC,GACjB,OAAoB,IAAbA,EAAEC,OAAe,IAAMD,EAAIX,OAAOW,EAC7C,CC5DO,SAASE,EAASC,EAAGC,EAAGC,GAC3BF,EAAIzB,EAAQyB,EAAG,KACfC,EAAI1B,EAAQ0B,EAAG,KACfC,EAAI3B,EAAQ2B,EAAG,KACf,IAAIzB,EAAMM,KAAKN,IAAIuB,EAAGC,EAAGC,GACrBlB,EAAMD,KAAKC,IAAIgB,EAAGC,EAAGC,GACrBC,EAAI,EACJC,EAAI,EACJC,GAAK5B,EAAMO,GAAO,EACtB,GAAIP,IAAQO,EACRoB,EAAI,EACJD,EAAI,MAEH,CACD,IAAIG,EAAI7B,EAAMO,EAEd,OADAoB,EAAIC,EAAI,GAAMC,GAAK,EAAI7B,EAAMO,GAAOsB,GAAK7B,EAAMO,GACvCP,GACJ,KAAKuB,EACDG,GAAKF,EAAIC,GAAKI,GAAKL,EAAIC,EAAI,EAAI,GAC/B,MACJ,KAAKD,EACDE,GAAKD,EAAIF,GAAKM,EAAI,EAClB,MACJ,KAAKJ,EACDC,GAAKH,EAAIC,GAAKK,EAAI,EAK1BH,GAAK,CACR,CACD,MAAO,CAAEA,EAAGA,EAAGC,EAAGA,EAAGC,EAAGA,EAC5B,CACA,SAASE,EAAQC,EAAGC,EAAGC,GAOnB,OANIA,EAAI,IACJA,GAAK,GAELA,EAAI,IACJA,GAAK,GAELA,EAAI,EAAI,EACDF,EAAe,EAAIE,GAAdD,EAAID,GAEhBE,EAAI,GACGD,EAEPC,EAAI,EAAI,EACDF,GAAKC,EAAID,IAAM,EAAI,EAAIE,GAAK,EAEhCF,CACX,CAmCO,SAASG,EAASX,EAAGC,EAAGC,GAC3BF,EAAIzB,EAAQyB,EAAG,KACfC,EAAI1B,EAAQ0B,EAAG,KACfC,EAAI3B,EAAQ2B,EAAG,KACf,IAAIzB,EAAMM,KAAKN,IAAIuB,EAAGC,EAAGC,GACrBlB,EAAMD,KAAKC,IAAIgB,EAAGC,EAAGC,GACrBC,EAAI,EACJS,EAAInC,EACJ6B,EAAI7B,EAAMO,EACVoB,EAAY,IAAR3B,EAAY,EAAI6B,EAAI7B,EAC5B,GAAIA,IAAQO,EACRmB,EAAI,MAEH,CACD,OAAQ1B,GACJ,KAAKuB,EACDG,GAAKF,EAAIC,GAAKI,GAAKL,EAAIC,EAAI,EAAI,GAC/B,MACJ,KAAKD,EACDE,GAAKD,EAAIF,GAAKM,EAAI,EAClB,MACJ,KAAKJ,EACDC,GAAKH,EAAIC,GAAKK,EAAI,EAK1BH,GAAK,CACR,CACD,MAAO,CAAEA,EAAGA,EAAGC,EAAGA,EAAGQ,EAAGA,EAC5B,CA4BO,SAASC,EAASb,EAAGC,EAAGC,EAAGY,GAC9B,IAAIC,EAAM,CACNnB,EAAKb,KAAKiC,MAAMhB,GAAGiB,SAAS,KAC5BrB,EAAKb,KAAKiC,MAAMf,GAAGgB,SAAS,KAC5BrB,EAAKb,KAAKiC,MAAMd,GAAGe,SAAS,MAGhC,OAAIH,GACAC,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,IACzBJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAExDJ,EAAIK,KAAK,GACpB,CA6BO,SAASC,EAAcrB,EAAGC,EAAGC,EAAGX,GAOnC,MANU,CACNK,EAAK0B,EAAoB/B,IACzBK,EAAKb,KAAKiC,MAAMhB,GAAGiB,SAAS,KAC5BrB,EAAKb,KAAKiC,MAAMf,GAAGgB,SAAS,KAC5BrB,EAAKb,KAAKiC,MAAMd,GAAGe,SAAS,MAErBG,KAAK,GACpB,CAEO,SAASE,EAAoBhB,GAChC,OAAOvB,KAAKiC,MAAsB,IAAhBrC,WAAW2B,IAAUW,SAAS,GACpD,CAEO,SAASM,EAAoBpB,GAChC,OAAOqB,EAAgBrB,GAAK,GAChC,CAEO,SAASqB,EAAgBnC,GAC5B,OAAOJ,SAASI,EAAK,GACzB,CC7MO,SAASoC,EAAWC,GACvB,IDdqB1B,EAAGC,EAAGC,ECcvByB,EAAM,CAAE3B,EAAG,EAAGC,EAAG,EAAGC,EAAG,GACvBX,EAAI,EACJa,EAAI,KACJQ,EAAI,KACJP,EAAI,KACJuB,GAAK,EACLC,GAAS,EA6Bb,MA5BqB,iBAAVH,IACPA,EAAQI,EAAoBJ,IAEX,iBAAVA,IACHK,EAAeL,EAAM1B,IAAM+B,EAAeL,EAAMzB,IAAM8B,EAAeL,EAAMxB,IDzB9DF,EC0BE0B,EAAM1B,ED1BLC,EC0BQyB,EAAMzB,ED1BXC,EC0BcwB,EAAMxB,EAAvCyB,EDzBD,CACH3B,EAAqB,IAAlBzB,EAAQyB,EAAG,KACdC,EAAqB,IAAlB1B,EAAQ0B,EAAG,KACdC,EAAqB,IAAlB3B,EAAQ2B,EAAG,MCuBV0B,GAAK,EACLC,EAAwC,MAA/B3C,OAAOwC,EAAM1B,GAAGgC,QAAQ,GAAa,OAAS,OAElDD,EAAeL,EAAMvB,IAAM4B,EAAeL,EAAMtB,IAAM2B,EAAeL,EAAMd,IAChFR,EAAIX,EAAoBiC,EAAMtB,GAC9BQ,EAAInB,EAAoBiC,EAAMd,GAC9Be,EDqGL,SAAkBxB,EAAGC,EAAGQ,GAC3BT,EAAsB,EAAlB5B,EAAQ4B,EAAG,KACfC,EAAI7B,EAAQ6B,EAAG,KACfQ,EAAIrC,EAAQqC,EAAG,KACf,IAAIqB,EAAIlD,KAAKmD,MAAM/B,GACfgC,EAAIhC,EAAI8B,EACRzB,EAAII,GAAK,EAAIR,GACbK,EAAIG,GAAK,EAAIuB,EAAI/B,GACjBM,EAAIE,GAAK,GAAK,EAAIuB,GAAK/B,GACvBgC,EAAMH,EAAI,EAId,MAAO,CAAEjC,EAAO,IAHR,CAACY,EAAGH,EAAGD,EAAGA,EAAGE,EAAGE,GAAGwB,GAGNnC,EAAO,IAFpB,CAACS,EAAGE,EAAGA,EAAGH,EAAGD,EAAGA,GAAG4B,GAEMlC,EAAO,IADhC,CAACM,EAAGA,EAAGE,EAAGE,EAAGA,EAAGH,GAAG2B,GAE/B,CCnHkBC,CAASX,EAAMvB,EAAGC,EAAGQ,GAC3BgB,GAAK,EACLC,EAAS,OAEJE,EAAeL,EAAMvB,IAAM4B,EAAeL,EAAMtB,IAAM2B,EAAeL,EAAMrB,KAChFD,EAAIX,EAAoBiC,EAAMtB,GAC9BC,EAAIZ,EAAoBiC,EAAMrB,GAC9BsB,ED6BL,SAAkBxB,EAAGC,EAAGC,GAC3B,IAAIL,EACAC,EACAC,EAIJ,GAHAC,EAAI5B,EAAQ4B,EAAG,KACfC,EAAI7B,EAAQ6B,EAAG,KACfC,EAAI9B,EAAQ8B,EAAG,KACL,IAAND,EAEAH,EAAII,EACJH,EAAIG,EACJL,EAAIK,MAEH,CACD,IAAII,EAAIJ,EAAI,GAAMA,GAAK,EAAID,GAAKC,EAAID,EAAIC,EAAID,EACxCI,EAAI,EAAIH,EAAII,EAChBT,EAAIO,EAAQC,EAAGC,EAAGN,EAAI,EAAI,GAC1BF,EAAIM,EAAQC,EAAGC,EAAGN,GAClBD,EAAIK,EAAQC,EAAGC,EAAGN,EAAI,EAAI,EAC7B,CACD,MAAO,CAAEH,EAAO,IAAJA,EAASC,EAAO,IAAJA,EAASC,EAAO,IAAJA,EACxC,CClDkBoC,CAASZ,EAAMvB,EAAGC,EAAGC,GAC3BuB,GAAK,EACLC,EAAS,OAETU,OAAOC,UAAUC,eAAeC,KAAKhB,EAAO,OAC5CnC,EAAImC,EAAMnC,IAGlBA,EAAID,EAAWC,GACR,CACHqC,GAAIA,EACJC,OAAQH,EAAMG,QAAUA,EACxB7B,EAAGjB,KAAKC,IAAI,IAAKD,KAAKN,IAAIkD,EAAI3B,EAAG,IACjCC,EAAGlB,KAAKC,IAAI,IAAKD,KAAKN,IAAIkD,EAAI1B,EAAG,IACjCC,EAAGnB,KAAKC,IAAI,IAAKD,KAAKN,IAAIkD,EAAIzB,EAAG,IACjCX,EAAGA,EAEX,CAEA,IAIIoD,EAAW,MAAMjD,OAFJ,uBAEuB,SAASA,OAJ/B,gBAImD,KAIjEkD,EAAoB,cAAclD,OAAOiD,EAAU,cAAcjD,OAAOiD,EAAU,cAAcjD,OAAOiD,EAAU,aACjHE,EAAoB,cAAcnD,OAAOiD,EAAU,cAAcjD,OAAOiD,EAAU,cAAcjD,OAAOiD,EAAU,cAAcjD,OAAOiD,EAAU,aAChJG,EAAW,CACXH,SAAU,IAAII,OAAOJ,GACrBhB,IAAK,IAAIoB,OAAO,MAAQH,GACxBI,KAAM,IAAID,OAAO,OAASF,GAC1BI,IAAK,IAAIF,OAAO,MAAQH,GACxBM,KAAM,IAAIH,OAAO,OAASF,GAC1BM,IAAK,IAAIJ,OAAO,MAAQH,GACxBQ,KAAM,IAAIL,OAAO,OAASF,GAC1BQ,KAAM,uDACNC,KAAM,uDACNC,KAAM,uEACNC,KAAM,wEAMH,SAAS1B,EAAoBJ,GAEhC,GAAqB,KADrBA,EAAQA,EAAM+B,OAAOC,eACX5D,OACN,OAAO,EAEX,IAAI6D,GAAQ,EACZ,GAAIzO,EAAMwM,GACNA,EAAQxM,EAAMwM,GACdiC,GAAQ,OAEP,GAAc,gBAAVjC,EACL,MAAO,CAAE1B,EAAG,EAAGC,EAAG,EAAGC,EAAG,EAAGX,EAAG,EAAGsC,OAAQ,QAM7C,IAAI+B,EAAQd,EAASnB,IAAIkC,KAAKnC,GAC9B,OAAIkC,EACO,CAAE5D,EAAG4D,EAAM,GAAI3D,EAAG2D,EAAM,GAAI1D,EAAG0D,EAAM,KAEhDA,EAAQd,EAASE,KAAKa,KAAKnC,IAEhB,CAAE1B,EAAG4D,EAAM,GAAI3D,EAAG2D,EAAM,GAAI1D,EAAG0D,EAAM,GAAIrE,EAAGqE,EAAM,KAE7DA,EAAQd,EAASG,IAAIY,KAAKnC,IAEf,CAAEvB,EAAGyD,EAAM,GAAIxD,EAAGwD,EAAM,GAAIvD,EAAGuD,EAAM,KAEhDA,EAAQd,EAASI,KAAKW,KAAKnC,IAEhB,CAAEvB,EAAGyD,EAAM,GAAIxD,EAAGwD,EAAM,GAAIvD,EAAGuD,EAAM,GAAIrE,EAAGqE,EAAM,KAE7DA,EAAQd,EAASK,IAAIU,KAAKnC,IAEf,CAAEvB,EAAGyD,EAAM,GAAIxD,EAAGwD,EAAM,GAAIhD,EAAGgD,EAAM,KAEhDA,EAAQd,EAASM,KAAKS,KAAKnC,IAEhB,CAAEvB,EAAGyD,EAAM,GAAIxD,EAAGwD,EAAM,GAAIhD,EAAGgD,EAAM,GAAIrE,EAAGqE,EAAM,KAE7DA,EAAQd,EAASU,KAAKK,KAAKnC,IAEhB,CACH1B,EAAGwB,EAAgBoC,EAAM,IACzB3D,EAAGuB,EAAgBoC,EAAM,IACzB1D,EAAGsB,EAAgBoC,EAAM,IACzBrE,EAAGgC,EAAoBqC,EAAM,IAC7B/B,OAAQ8B,EAAQ,OAAS,SAGjCC,EAAQd,EAASQ,KAAKO,KAAKnC,IAEhB,CACH1B,EAAGwB,EAAgBoC,EAAM,IACzB3D,EAAGuB,EAAgBoC,EAAM,IACzB1D,EAAGsB,EAAgBoC,EAAM,IACzB/B,OAAQ8B,EAAQ,OAAS,QAGjCC,EAAQd,EAASS,KAAKM,KAAKnC,IAEhB,CACH1B,EAAGwB,EAAgBoC,EAAM,GAAKA,EAAM,IACpC3D,EAAGuB,EAAgBoC,EAAM,GAAKA,EAAM,IACpC1D,EAAGsB,EAAgBoC,EAAM,GAAKA,EAAM,IACpCrE,EAAGgC,EAAoBqC,EAAM,GAAKA,EAAM,IACxC/B,OAAQ8B,EAAQ,OAAS,WAGjCC,EAAQd,EAASO,KAAKQ,KAAKnC,KAEhB,CACH1B,EAAGwB,EAAgBoC,EAAM,GAAKA,EAAM,IACpC3D,EAAGuB,EAAgBoC,EAAM,GAAKA,EAAM,IACpC1D,EAAGsB,EAAgBoC,EAAM,GAAKA,EAAM,IACpC/B,OAAQ8B,EAAQ,OAAS,MAIrC,CAKO,SAAS5B,EAAeL,GAC3B,OAAOoC,QAAQhB,EAASH,SAASkB,KAAK3E,OAAOwC,IACjD,CClLA,IAAIqC,EAA2B,WAC3B,SAASA,EAAUrC,EAAOsC,GAGtB,IAAIC,EAEJ,QAJc,IAAVvC,IAAoBA,EAAQ,SACnB,IAATsC,IAAmBA,EAAO,CAAE,GAG5BtC,aAAiBqC,EAEjB,OAAOrC,EAEU,iBAAVA,IACPA,EFqNL,SAA6BA,GAChC,MAAO,CACH1B,EAAG0B,GAAS,GACZzB,GAAY,MAARyB,IAAmB,EACvBxB,EAAW,IAARwB,EAEX,CE3NoBwC,CAAoBxC,IAEhCyC,KAAKC,cAAgB1C,EACrB,IAAIC,EAAMF,EAAWC,GACrByC,KAAKC,cAAgB1C,EACrByC,KAAKnE,EAAI2B,EAAI3B,EACbmE,KAAKlE,EAAI0B,EAAI1B,EACbkE,KAAKjE,EAAIyB,EAAIzB,EACbiE,KAAK5E,EAAIoC,EAAIpC,EACb4E,KAAKE,OAAStF,KAAKiC,MAAM,IAAMmD,KAAK5E,GAAK,IACzC4E,KAAKtC,OAAgC,QAAtBoC,EAAKD,EAAKnC,cAA2B,IAAPoC,EAAgBA,EAAKtC,EAAIE,OACtEsC,KAAKG,aAAeN,EAAKM,aAKrBH,KAAKnE,EAAI,IACTmE,KAAKnE,EAAIjB,KAAKiC,MAAMmD,KAAKnE,IAEzBmE,KAAKlE,EAAI,IACTkE,KAAKlE,EAAIlB,KAAKiC,MAAMmD,KAAKlE,IAEzBkE,KAAKjE,EAAI,IACTiE,KAAKjE,EAAInB,KAAKiC,MAAMmD,KAAKjE,IAE7BiE,KAAKI,QAAU5C,EAAIC,EACtB,CA0cD,OAzcAmC,EAAUvB,UAAUgC,OAAS,WACzB,OAAOL,KAAKM,gBAAkB,GACtC,EACIV,EAAUvB,UAAUkC,QAAU,WAC1B,OAAQP,KAAKK,QACrB,EAIIT,EAAUvB,UAAUiC,cAAgB,WAEhC,IAAI9C,EAAMwC,KAAKQ,QACf,OAAgB,IAARhD,EAAI3B,EAAkB,IAAR2B,EAAI1B,EAAkB,IAAR0B,EAAIzB,GAAW,GAC3D,EAII6D,EAAUvB,UAAUoC,aAAe,WAE/B,IAAIjD,EAAMwC,KAAKQ,QAIXE,EAAQlD,EAAI3B,EAAI,IAChB8E,EAAQnD,EAAI1B,EAAI,IAChB8E,EAAQpD,EAAIzB,EAAI,IAsBpB,MAAO,OArBH2E,GAAS,OACLA,EAAQ,MAIR9F,KAAKiG,KAAKH,EAAQ,MAAS,MAAO,MAgBtB,OAdhBC,GAAS,OACLA,EAAQ,MAIR/F,KAAKiG,KAAKF,EAAQ,MAAS,MAAO,MAST,OAP7BC,GAAS,OACLA,EAAQ,MAIRhG,KAAKiG,KAAKD,EAAQ,MAAS,MAAO,KAGlD,EAIIhB,EAAUvB,UAAUyC,SAAW,WAC3B,OAAOd,KAAK5E,CACpB,EAMIwE,EAAUvB,UAAU0C,SAAW,SAAUC,GAGrC,OAFAhB,KAAK5E,EAAID,EAAW6F,GACpBhB,KAAKE,OAAStF,KAAKiC,MAAM,IAAMmD,KAAK5E,GAAK,IAClC4E,IACf,EAIIJ,EAAUvB,UAAU4C,aAAe,WAE/B,OAAa,IADLjB,KAAKkB,QAAQjF,CAE7B,EAII2D,EAAUvB,UAAU8C,MAAQ,WACxB,IAAInC,EAAMxC,EAASwD,KAAKnE,EAAGmE,KAAKlE,EAAGkE,KAAKjE,GACxC,MAAO,CAAEC,EAAW,IAARgD,EAAIhD,EAASC,EAAG+C,EAAI/C,EAAGQ,EAAGuC,EAAIvC,EAAGrB,EAAG4E,KAAK5E,EAC7D,EAKIwE,EAAUvB,UAAU+C,YAAc,WAC9B,IAAIpC,EAAMxC,EAASwD,KAAKnE,EAAGmE,KAAKlE,EAAGkE,KAAKjE,GACpCC,EAAIpB,KAAKiC,MAAc,IAARmC,EAAIhD,GACnBC,EAAIrB,KAAKiC,MAAc,IAARmC,EAAI/C,GACnBQ,EAAI7B,KAAKiC,MAAc,IAARmC,EAAIvC,GACvB,OAAkB,IAAXuD,KAAK5E,EAAU,OAAOG,OAAOS,EAAG,MAAMT,OAAOU,EAAG,OAAOV,OAAOkB,EAAG,MAAQ,QAAQlB,OAAOS,EAAG,MAAMT,OAAOU,EAAG,OAAOV,OAAOkB,EAAG,OAAOlB,OAAOyE,KAAKE,OAAQ,IACtK,EAIIN,EAAUvB,UAAU6C,MAAQ,WACxB,IAAIpC,EAAMlD,EAASoE,KAAKnE,EAAGmE,KAAKlE,EAAGkE,KAAKjE,GACxC,MAAO,CAAEC,EAAW,IAAR8C,EAAI9C,EAASC,EAAG6C,EAAI7C,EAAGC,EAAG4C,EAAI5C,EAAGd,EAAG4E,KAAK5E,EAC7D,EAKIwE,EAAUvB,UAAUgD,YAAc,WAC9B,IAAIvC,EAAMlD,EAASoE,KAAKnE,EAAGmE,KAAKlE,EAAGkE,KAAKjE,GACpCC,EAAIpB,KAAKiC,MAAc,IAARiC,EAAI9C,GACnBC,EAAIrB,KAAKiC,MAAc,IAARiC,EAAI7C,GACnBC,EAAItB,KAAKiC,MAAc,IAARiC,EAAI5C,GACvB,OAAkB,IAAX8D,KAAK5E,EAAU,OAAOG,OAAOS,EAAG,MAAMT,OAAOU,EAAG,OAAOV,OAAOW,EAAG,MAAQ,QAAQX,OAAOS,EAAG,MAAMT,OAAOU,EAAG,OAAOV,OAAOW,EAAG,OAAOX,OAAOyE,KAAKE,OAAQ,IACtK,EAKIN,EAAUvB,UAAUiD,MAAQ,SAAU3E,GAElC,YADmB,IAAfA,IAAyBA,GAAa,GACnCD,EAASsD,KAAKnE,EAAGmE,KAAKlE,EAAGkE,KAAKjE,EAAGY,EAChD,EAKIiD,EAAUvB,UAAUkD,YAAc,SAAU5E,GAExC,YADmB,IAAfA,IAAyBA,GAAa,GACnC,IAAMqD,KAAKsB,MAAM3E,EAChC,EAKIiD,EAAUvB,UAAUmD,OAAS,SAAUC,GAEnC,YADmB,IAAfA,IAAyBA,GAAa,GFe3C,SAAmB5F,EAAGC,EAAGC,EAAGX,EAAGqG,GAClC,IAAI7E,EAAM,CACNnB,EAAKb,KAAKiC,MAAMhB,GAAGiB,SAAS,KAC5BrB,EAAKb,KAAKiC,MAAMf,GAAGgB,SAAS,KAC5BrB,EAAKb,KAAKiC,MAAMd,GAAGe,SAAS,KAC5BrB,EAAK0B,EAAoB/B,KAG7B,OAAIqG,GACA7E,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,KAChCJ,EAAI,GAAGG,WAAWH,EAAI,GAAGI,OAAO,IACzBJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAAKJ,EAAI,GAAGI,OAAO,GAE3EJ,EAAIK,KAAK,GACpB,CE9BeyE,CAAU1B,KAAKnE,EAAGmE,KAAKlE,EAAGkE,KAAKjE,EAAGiE,KAAK5E,EAAGqG,EACzD,EAKI7B,EAAUvB,UAAUsD,aAAe,SAAUF,GAEzC,YADmB,IAAfA,IAAyBA,GAAa,GACnC,IAAMzB,KAAKwB,OAAOC,EACjC,EAKI7B,EAAUvB,UAAUuD,iBAAmB,SAAUC,GAE7C,YADuB,IAAnBA,IAA6BA,GAAiB,GAChC,IAAX7B,KAAK5E,EAAU4E,KAAKuB,YAAYM,GAAkB7B,KAAK2B,aAAaE,EACnF,EAIIjC,EAAUvB,UAAUmC,MAAQ,WACxB,MAAO,CACH3E,EAAGjB,KAAKiC,MAAMmD,KAAKnE,GACnBC,EAAGlB,KAAKiC,MAAMmD,KAAKlE,GACnBC,EAAGnB,KAAKiC,MAAMmD,KAAKjE,GACnBX,EAAG4E,KAAK5E,EAEpB,EAKIwE,EAAUvB,UAAUyD,YAAc,WAC9B,IAAIjG,EAAIjB,KAAKiC,MAAMmD,KAAKnE,GACpBC,EAAIlB,KAAKiC,MAAMmD,KAAKlE,GACpBC,EAAInB,KAAKiC,MAAMmD,KAAKjE,GACxB,OAAkB,IAAXiE,KAAK5E,EAAU,OAAOG,OAAOM,EAAG,MAAMN,OAAOO,EAAG,MAAMP,OAAOQ,EAAG,KAAO,QAAQR,OAAOM,EAAG,MAAMN,OAAOO,EAAG,MAAMP,OAAOQ,EAAG,MAAMR,OAAOyE,KAAKE,OAAQ,IAClK,EAIIN,EAAUvB,UAAU0D,gBAAkB,WAClC,IAAIC,EAAM,SAAUC,GAAK,MAAO,GAAG1G,OAAOX,KAAKiC,MAAwB,IAAlBzC,EAAQ6H,EAAG,MAAa,MAC7E,MAAO,CACHpG,EAAGmG,EAAIhC,KAAKnE,GACZC,EAAGkG,EAAIhC,KAAKlE,GACZC,EAAGiG,EAAIhC,KAAKjE,GACZX,EAAG4E,KAAK5E,EAEpB,EAIIwE,EAAUvB,UAAU6D,sBAAwB,WACxC,IAAIC,EAAM,SAAUF,GAAK,OAAOrH,KAAKiC,MAAwB,IAAlBzC,EAAQ6H,EAAG,KAAY,EAClE,OAAkB,IAAXjC,KAAK5E,EACN,OAAOG,OAAO4G,EAAInC,KAAKnE,GAAI,OAAON,OAAO4G,EAAInC,KAAKlE,GAAI,OAAOP,OAAO4G,EAAInC,KAAKjE,GAAI,MACjF,QAAQR,OAAO4G,EAAInC,KAAKnE,GAAI,OAAON,OAAO4G,EAAInC,KAAKlE,GAAI,OAAOP,OAAO4G,EAAInC,KAAKjE,GAAI,OAAOR,OAAOyE,KAAKE,OAAQ,IAC3H,EAIIN,EAAUvB,UAAU+D,OAAS,WACzB,GAAe,IAAXpC,KAAK5E,EACL,MAAO,cAEX,GAAI4E,KAAK5E,EAAI,EACT,OAAO,EAGX,IADA,IAAIwB,EAAM,IAAMF,EAASsD,KAAKnE,EAAGmE,KAAKlE,EAAGkE,KAAKjE,GAAG,GACxCsG,EAAK,EAAGvC,EAAK1B,OAAOkE,QAAQvR,GAAQsR,EAAKvC,EAAGnE,OAAQ0G,IAAM,CAC/D,IAAIE,EAAKzC,EAAGuC,GAAKG,EAAMD,EAAG,GAC1B,GAAI3F,IADkC2F,EAAG,GAErC,OAAOC,CAEd,CACD,OAAO,CACf,EACI5C,EAAUvB,UAAUvB,SAAW,SAAUY,GACrC,IAAI+E,EAAY9C,QAAQjC,GACxBA,EAASA,QAAuCA,EAASsC,KAAKtC,OAC9D,IAAIgF,GAAkB,EAClBC,EAAW3C,KAAK5E,EAAI,GAAK4E,KAAK5E,GAAK,EAEvC,OADwBqH,IAAaE,IAAajF,EAAOX,WAAW,QAAqB,SAAXW,GAS/D,QAAXA,IACAgF,EAAkB1C,KAAK8B,eAEZ,SAAXpE,IACAgF,EAAkB1C,KAAKkC,yBAEZ,QAAXxE,GAA+B,SAAXA,IACpBgF,EAAkB1C,KAAKuB,eAEZ,SAAX7D,IACAgF,EAAkB1C,KAAKuB,aAAY,IAExB,SAAX7D,IACAgF,EAAkB1C,KAAK2B,cAAa,IAEzB,SAAXjE,IACAgF,EAAkB1C,KAAK2B,gBAEZ,SAAXjE,IACAgF,EAAkB1C,KAAKoC,UAEZ,QAAX1E,IACAgF,EAAkB1C,KAAKqB,eAEZ,QAAX3D,IACAgF,EAAkB1C,KAAKoB,eAEpBsB,GAAmB1C,KAAKuB,eAhCZ,SAAX7D,GAAgC,IAAXsC,KAAK5E,EACnB4E,KAAKoC,SAETpC,KAAK8B,aA8BxB,EACIlC,EAAUvB,UAAUuE,SAAW,WAC3B,OAAQhI,KAAKiC,MAAMmD,KAAKnE,IAAM,KAAOjB,KAAKiC,MAAMmD,KAAKlE,IAAM,GAAKlB,KAAKiC,MAAMmD,KAAKjE,EACxF,EACI6D,EAAUvB,UAAUwE,MAAQ,WACxB,OAAO,IAAIjD,EAAUI,KAAKlD,WAClC,EAKI8C,EAAUvB,UAAUyE,QAAU,SAAUC,QACrB,IAAXA,IAAqBA,EAAS,IAClC,IAAIjE,EAAMkB,KAAKkB,QAGf,OAFApC,EAAI5C,GAAK6G,EAAS,IAClBjE,EAAI5C,EAAIjB,EAAQ6D,EAAI5C,GACb,IAAI0D,EAAUd,EAC7B,EAKIc,EAAUvB,UAAU2E,SAAW,SAAUD,QACtB,IAAXA,IAAqBA,EAAS,IAClC,IAAIvF,EAAMwC,KAAKQ,QAIf,OAHAhD,EAAI3B,EAAIjB,KAAKN,IAAI,EAAGM,KAAKC,IAAI,IAAK2C,EAAI3B,EAAIjB,KAAKiC,OAAckG,EAAS,IAAjB,OACrDvF,EAAI1B,EAAIlB,KAAKN,IAAI,EAAGM,KAAKC,IAAI,IAAK2C,EAAI1B,EAAIlB,KAAKiC,OAAckG,EAAS,IAAjB,OACrDvF,EAAIzB,EAAInB,KAAKN,IAAI,EAAGM,KAAKC,IAAI,IAAK2C,EAAIzB,EAAInB,KAAKiC,OAAckG,EAAS,IAAjB,OAC9C,IAAInD,EAAUpC,EAC7B,EAMIoC,EAAUvB,UAAU4E,OAAS,SAAUF,QACpB,IAAXA,IAAqBA,EAAS,IAClC,IAAIjE,EAAMkB,KAAKkB,QAGf,OAFApC,EAAI5C,GAAK6G,EAAS,IAClBjE,EAAI5C,EAAIjB,EAAQ6D,EAAI5C,GACb,IAAI0D,EAAUd,EAC7B,EAMIc,EAAUvB,UAAU6E,KAAO,SAAUH,GAEjC,YADe,IAAXA,IAAqBA,EAAS,IAC3B/C,KAAKmD,IAAI,QAASJ,EACjC,EAMInD,EAAUvB,UAAU+E,MAAQ,SAAUL,GAElC,YADe,IAAXA,IAAqBA,EAAS,IAC3B/C,KAAKmD,IAAI,QAASJ,EACjC,EAMInD,EAAUvB,UAAUgF,WAAa,SAAUN,QACxB,IAAXA,IAAqBA,EAAS,IAClC,IAAIjE,EAAMkB,KAAKkB,QAGf,OAFApC,EAAI7C,GAAK8G,EAAS,IAClBjE,EAAI7C,EAAIhB,EAAQ6D,EAAI7C,GACb,IAAI2D,EAAUd,EAC7B,EAKIc,EAAUvB,UAAUiF,SAAW,SAAUP,QACtB,IAAXA,IAAqBA,EAAS,IAClC,IAAIjE,EAAMkB,KAAKkB,QAGf,OAFApC,EAAI7C,GAAK8G,EAAS,IAClBjE,EAAI7C,EAAIhB,EAAQ6D,EAAI7C,GACb,IAAI2D,EAAUd,EAC7B,EAKIc,EAAUvB,UAAUkF,UAAY,WAC5B,OAAOvD,KAAKqD,WAAW,IAC/B,EAKIzD,EAAUvB,UAAUmF,KAAO,SAAUT,GACjC,IAAIjE,EAAMkB,KAAKkB,QACXuC,GAAO3E,EAAI9C,EAAI+G,GAAU,IAE7B,OADAjE,EAAI9C,EAAIyH,EAAM,EAAI,IAAMA,EAAMA,EACvB,IAAI7D,EAAUd,EAC7B,EAKIc,EAAUvB,UAAU8E,IAAM,SAAU5F,EAAOwF,QACxB,IAAXA,IAAqBA,EAAS,IAClC,IAAIW,EAAO1D,KAAKQ,QACZmD,EAAO,IAAI/D,EAAUrC,GAAOiD,QAC5BnE,EAAI0G,EAAS,IAOjB,OAAO,IAAInD,EANA,CACP/D,GAAI8H,EAAK9H,EAAI6H,EAAK7H,GAAKQ,EAAIqH,EAAK7H,EAChCC,GAAI6H,EAAK7H,EAAI4H,EAAK5H,GAAKO,EAAIqH,EAAK5H,EAChCC,GAAI4H,EAAK5H,EAAI2H,EAAK3H,GAAKM,EAAIqH,EAAK3H,EAChCX,GAAIuI,EAAKvI,EAAIsI,EAAKtI,GAAKiB,EAAIqH,EAAKtI,GAG5C,EACIwE,EAAUvB,UAAUuF,UAAY,SAAUC,EAASC,QAC/B,IAAZD,IAAsBA,EAAU,QACrB,IAAXC,IAAqBA,EAAS,IAClC,IAAIhF,EAAMkB,KAAKkB,QACX6C,EAAO,IAAMD,EACbE,EAAM,CAAChE,MACX,IAAKlB,EAAI9C,GAAK8C,EAAI9C,GAAM+H,EAAOF,GAAY,GAAK,KAAO,MAAOA,GAC1D/E,EAAI9C,GAAK8C,EAAI9C,EAAI+H,GAAQ,IACzBC,EAAIC,KAAK,IAAIrE,EAAUd,IAE3B,OAAOkF,CACf,EAIIpE,EAAUvB,UAAU6F,WAAa,WAC7B,IAAIpF,EAAMkB,KAAKkB,QAEf,OADApC,EAAI9C,GAAK8C,EAAI9C,EAAI,KAAO,IACjB,IAAI4D,EAAUd,EAC7B,EACIc,EAAUvB,UAAU8F,cAAgB,SAAUN,QAC1B,IAAZA,IAAsBA,EAAU,GAOpC,IANA,IAAI7E,EAAMgB,KAAKmB,QACXnF,EAAIgD,EAAIhD,EACRC,EAAI+C,EAAI/C,EACRQ,EAAIuC,EAAIvC,EACR2H,EAAM,GACNC,EAAe,EAAIR,EAChBA,KACHO,EAAIH,KAAK,IAAIrE,EAAU,CAAE5D,EAAGA,EAAGC,EAAGA,EAAGQ,EAAGA,KACxCA,GAAKA,EAAI4H,GAAgB,EAE7B,OAAOD,CACf,EACIxE,EAAUvB,UAAUiG,gBAAkB,WAClC,IAAIxF,EAAMkB,KAAKkB,QACXlF,EAAI8C,EAAI9C,EACZ,MAAO,CACHgE,KACA,IAAIJ,EAAU,CAAE5D,GAAIA,EAAI,IAAM,IAAKC,EAAG6C,EAAI7C,EAAGC,EAAG4C,EAAI5C,IACpD,IAAI0D,EAAU,CAAE5D,GAAIA,EAAI,KAAO,IAAKC,EAAG6C,EAAI7C,EAAGC,EAAG4C,EAAI5C,IAEjE,EAII0D,EAAUvB,UAAUkG,aAAe,SAAUC,GACzC,IAAIC,EAAKzE,KAAKQ,QACVkE,EAAK,IAAI9E,EAAU4E,GAAYhE,QAC/BQ,EAAQyD,EAAGrJ,EAAIsJ,EAAGtJ,GAAK,EAAIqJ,EAAGrJ,GAClC,OAAO,IAAIwE,EAAU,CACjB/D,GAAI4I,EAAG5I,EAAI4I,EAAGrJ,EAAIsJ,EAAG7I,EAAI6I,EAAGtJ,GAAK,EAAIqJ,EAAGrJ,IAAM4F,EAC9ClF,GAAI2I,EAAG3I,EAAI2I,EAAGrJ,EAAIsJ,EAAG5I,EAAI4I,EAAGtJ,GAAK,EAAIqJ,EAAGrJ,IAAM4F,EAC9CjF,GAAI0I,EAAG1I,EAAI0I,EAAGrJ,EAAIsJ,EAAG3I,EAAI2I,EAAGtJ,GAAK,EAAIqJ,EAAGrJ,IAAM4F,EAC9C5F,EAAG4F,GAEf,EAIIpB,EAAUvB,UAAUsG,MAAQ,WACxB,OAAO3E,KAAK4E,OAAO,EAC3B,EAIIhF,EAAUvB,UAAUwG,OAAS,WACzB,OAAO7E,KAAK4E,OAAO,EAC3B,EAKIhF,EAAUvB,UAAUuG,OAAS,SAAUvK,GAKnC,IAJA,IAAIyE,EAAMkB,KAAKkB,QACXlF,EAAI8C,EAAI9C,EACR8I,EAAS,CAAC9E,MACV+E,EAAY,IAAM1K,EACbyD,EAAI,EAAGA,EAAIzD,EAAGyD,IACnBgH,EAAOb,KAAK,IAAIrE,EAAU,CAAE5D,GAAIA,EAAI8B,EAAIiH,GAAa,IAAK9I,EAAG6C,EAAI7C,EAAGC,EAAG4C,EAAI5C,KAE/E,OAAO4I,CACf,EAIIlF,EAAUvB,UAAU2G,OAAS,SAAUzH,GACnC,OAAOyC,KAAK8B,gBAAkB,IAAIlC,EAAUrC,GAAOuE,aAC3D,EACWlC,CACX,ICpXA,SAASqF,EAAaxB,GAEdA,GAAO,KAAOA,GAAO,MACrBA,GAAO,KAEX,IAAK,IAAIpB,EAAK,EAAG6C,EAAWC,EAAQ9C,EAAK6C,EAASvJ,OAAQ0G,IAAM,CAC5D,IACI9E,EAAQ6H,EADAF,EAAS7C,IAErB,GAAI9E,EAAM8H,UAAY5B,GAAOlG,EAAM8H,SAAS,IAAM5B,GAAOlG,EAAM8H,SAAS,GACpE,OAAO9H,CAEd,CACD,MAAM+H,MAAM,kBAChB,CACA,SAASC,EAAaC,EAAOC,GACzB,QAAaC,IAATD,EACA,OAAO7K,KAAKmD,MAAMyH,EAAM,GAAK5K,KAAK+K,UAAYH,EAAM,GAAK,EAAIA,EAAM,KAGvE,IAAIlL,EAAMkL,EAAM,IAAM,EAClB3K,EAAM2K,EAAM,IAAM,EAElBrD,GADJsD,GAAe,KAAPA,EAAc,OAAS,QACd,OACjB,OAAO7K,KAAKmD,MAAMlD,EAAMsH,GAAO7H,EAAMO,GACzC,CACA,SAASuK,EAAYQ,GACjB,IAAIC,EAAOD,EAAME,YAAY,GAAG,GAC5BC,EAAOH,EAAME,YAAYF,EAAME,YAAYnK,OAAS,GAAG,GACvDqK,EAAOJ,EAAME,YAAYF,EAAME,YAAYnK,OAAS,GAAG,GACvDsK,EAAOL,EAAME,YAAY,GAAG,GAChC,MAAO,CACHI,KAAMN,EAAMM,KACZb,SAAUO,EAAMP,SAChBS,YAAaF,EAAME,YACnBK,gBAAiB,CAACN,EAAME,GACxBK,gBAAiB,CAACJ,EAAMC,GAEhC,CAIO,IAAId,EAAS,CAChB,CACIe,KAAM,aACNb,SAAU,KACVS,YAAa,CACT,CAAC,EAAG,GACJ,CAAC,IAAK,KAGd,CACII,KAAM,MACNb,SAAU,EAAE,GAAI,IAChBS,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,SACNb,SAAU,CAAC,GAAI,IACfS,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,SACNb,SAAU,CAAC,GAAI,IACfS,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,QACNb,SAAU,CAAC,GAAI,KACfS,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,OACNb,SAAU,CAAC,IAAK,KAChBS,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,SACNb,SAAU,CAAC,IAAK,KAChBS,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,MAGd,CACII,KAAM,OACNb,SAAU,CAAC,IAAK,KAChBS,YAAa,CACT,CAAC,GAAI,KACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,GAAI,IACL,CAAC,IAAK,OCzQX,SAASO,EAAYC,EAAQC,GAChC,IAAIC,EAAK,IAAI5G,EAAU0G,GACnBG,EAAK,IAAI7G,EAAU2G,GACvB,OAAS3L,KAAKN,IAAIkM,EAAG/F,eAAgBgG,EAAGhG,gBAAkB,MACrD7F,KAAKC,IAAI2L,EAAG/F,eAAgBgG,EAAGhG,gBAAkB,IAC1D,CCPI,IAAAiG,EHgfG,SAAmBnJ,EAAOsC,GAG7B,YAFc,IAAVtC,IAAoBA,EAAQ,SACnB,IAATsC,IAAmBA,EAAO,CAAE,GACzB,IAAID,EAAUrC,EAAOsC,EAChC,SGnfA6G,EAAa9G,UAAYA,EACzB8G,EAAaL,YAAcA,EAC3BK,EAAaC,aDmDN,SAASA,EAAaC,EAAWC,EAAWC,QAClC,IAATA,IAAmBA,EAAO,CAAEC,uBAAuB,EAAOC,MAAO,KAAMC,KAAM,UAIjF,IAHA,IAAIC,EAAY,KACZC,EAAY,EACZJ,EAAwBD,EAAKC,sBAAuBC,EAAQF,EAAKE,MAAOC,EAAOH,EAAKG,KAC/E5E,EAAK,EAAG+E,EAAcP,EAAWxE,EAAK+E,EAAYzL,OAAQ0G,IAAM,CACrE,IAAI9E,EAAQ6J,EAAY/E,GACpBgF,EAAQhB,EAAYO,EAAWrJ,GAC/B8J,EAAQF,IACRA,EAAYE,EACZH,EAAY,IAAItH,EAAUrC,GAEjC,CACD,OA9CG,SAAoB+I,EAAQC,EAAQe,GACvC,IAAIxH,EAAIyC,OACM,IAAV+E,IAAoBA,EAAQ,CAAEN,MAAO,KAAMC,KAAM,UACrD,IAAIM,EAAmBlB,EAAYC,EAAQC,GAC3C,QAAgC,QAAtBzG,EAAKwH,EAAMN,aAA0B,IAAPlH,EAAgBA,EAAK,OAA+B,QAArByC,EAAK+E,EAAML,YAAyB,IAAP1E,EAAgBA,EAAK,UACrH,IAAK,UACL,IAAK,WACD,OAAOgF,GAAoB,IAC/B,IAAK,UACD,OAAOA,GAAoB,EAC/B,IAAK,WACD,OAAOA,GAAoB,EAC/B,QACI,OAAO,EAEnB,CA+BQC,CAAWZ,EAAWM,EAAW,CAAEF,MAAOA,EAAOC,KAAMA,MAAYF,EAC5DG,GAEXJ,EAAKC,uBAAwB,EACtBJ,EAAaC,EAAW,CAAC,OAAQ,QAASE,GACrD,ECpEAJ,EAAaf,OFPN,SAASA,EAAO8B,GAGnB,QAFgB,IAAZA,IAAsBA,EAAU,CAAE,QAEhB/B,IAAlB+B,EAAQC,OACU,OAAlBD,EAAQC,MAAgB,CACxB,IAAIC,EAAcF,EAAQC,MACtBE,EAAS,GAEb,IADAH,EAAQC,WAAQhC,EACTiC,EAAcC,EAAOjM,QAIxB8L,EAAQC,MAAQ,KACZD,EAAQhC,OACRgC,EAAQhC,MAAQ,GAEpBmC,EAAO3D,KAAK0B,EAAO8B,IAGvB,OADAA,EAAQC,MAAQC,EACTC,CACV,CAED,IAAI5L,EAYR,SAAiByH,EAAKgC,GAClB,IACIrB,EAAMmB,EAmEd,SAAqBsC,GACjB,IAAIC,EAAMhN,SAAS+M,EAAY,IAC/B,IAAKrM,OAAOH,MAAMyM,IAAQA,EAAM,KAAOA,EAAM,EACzC,MAAO,CAACA,EAAKA,GAEjB,GAA0B,iBAAfD,EAAyB,CAChC,IAAIE,EAAa5C,EAAO6C,MAAK,SAAU3N,GAAK,OAAOA,EAAE6L,OAAS2B,CAAW,IACzE,GAAIE,EAAY,CACZ,IAAIxK,EAAQ6H,EAAY2C,GACxB,GAAIxK,EAAM8H,SACN,OAAO9H,EAAM8H,QAEpB,CACD,IAAI4C,EAAS,IAAIrI,EAAUiI,GAC3B,GAAII,EAAO7H,QAAS,CAChB,IAAIqD,EAAMwE,EAAO9G,QAAQnF,EACzB,MAAO,CAACyH,EAAKA,EAChB,CACJ,CACD,MAAO,CAAC,EAAG,IACf,CAxFmByE,CAAYzE,GACMgC,GAG7BrB,EAAM,IACNA,EAAM,IAAMA,GAEhB,OAAOA,CACX,CArBY+D,CAAQV,EAAQhE,IAAKgE,EAAQhC,MAEjCxJ,EAoBR,SAAwBwH,EAAKgE,GACzB,GAAoB,eAAhBA,EAAQhE,IACR,OAAO,EAEX,GAA2B,WAAvBgE,EAAQW,WACR,OAAO7C,EAAa,CAAC,EAAG,KAAMkC,EAAQhC,MAE1C,IAAIU,EAAkBlB,EAAaxB,GAAK0C,gBACpCN,EAAOM,EAAgB,GACvBJ,EAAOI,EAAgB,GAC3B,OAAQsB,EAAQW,YACZ,IAAK,SACDvC,EAAO,GACP,MACJ,IAAK,OACDA,EAAOE,EAAO,GACd,MACJ,IAAK,QACDA,EAAO,GAKf,OAAOR,EAAa,CAACM,EAAME,GAAO0B,EAAQhC,KAC9C,CA5CY4C,CAAerM,EAAGyL,GAEtBhL,EA2CR,SAAwB6L,EAAGC,EAAGd,GAC1B,IAAIzB,EAkBR,SAA8BsC,EAAGC,GAE7B,IADA,IAAIzC,EAAcb,EAAaqD,GAAGxC,YACzBhI,EAAI,EAAGA,EAAIgI,EAAYnK,OAAS,EAAGmC,IAAK,CAC7C,IAAI0K,EAAK1C,EAAYhI,GAAG,GACpB2K,EAAK3C,EAAYhI,GAAG,GACpB4K,EAAK5C,EAAYhI,EAAI,GAAG,GACxB6K,EAAK7C,EAAYhI,EAAI,GAAG,GAC5B,GAAIyK,GAAKC,GAAMD,GAAKG,EAAI,CACpB,IAAIE,GAAKD,EAAKF,IAAOC,EAAKF,GAE1B,OAAOI,EAAIL,GADHE,EAAKG,EAAIJ,EAEpB,CACJ,CACD,OAAO,CACX,CAhCeK,CAAqBP,EAAGC,GAC/BtC,EAAO,IACX,OAAQwB,EAAQW,YACZ,IAAK,OACDnC,EAAOD,EAAO,GACd,MACJ,IAAK,QACDA,GAAQC,EAAOD,GAAQ,EACvB,MACJ,IAAK,SACDA,EAAO,EACPC,EAAO,IAKf,OAAOV,EAAa,CAACS,EAAMC,GAAOwB,EAAQhC,KAC9C,CA7DYqD,CAAe9M,EAAGC,EAAGwL,GACzBrD,EAAM,CAAEpI,EAAGA,EAAGC,EAAGA,EAAGQ,EAAGA,GAK3B,YAJsBiJ,IAAlB+B,EAAQzG,QACRoD,EAAIhJ,EAAIqM,EAAQzG,OAGb,IAAIpB,EAAUwE,EACzB,EEzBAsC,EAAa3V,MAAQA,EACrB2V,EAAaqC,UCPN,SAAmBC,EAAOnJ,GAC7B,IAAIoJ,EAAW,CACXpN,EAAGP,EAAoB0N,EAAMnN,GAC7BC,EAAGR,EAAoB0N,EAAMlN,GAC7BC,EAAGT,EAAoB0N,EAAMjN,IAKjC,YAHgB2J,IAAZsD,EAAM5N,IACN6N,EAAS7N,EAAII,OAAOwN,EAAM5N,IAEvB,IAAIwE,EAAUqJ,EAAUpJ,EACnC,EDFA6G,EAAawC,aCIN,WACH,OAAO,IAAItJ,EAAU,CACjB/D,EAAGjB,KAAK+K,SACR7J,EAAGlB,KAAK+K,SACR5J,EAAGnB,KAAK+K,UAEhB,EDTAe,EAAayC,WEVN,SAAoBC,EAAYC,GACnC,IAAI9L,EAAQ,IAAIqC,EAAUwJ,GACtBE,EAAa,IAAMpM,EAAcK,EAAM1B,EAAG0B,EAAMzB,EAAGyB,EAAMxB,EAAGwB,EAAMnC,GAClEmO,EAAmBD,EACnBnJ,EAAe5C,EAAM4C,aAAe,qBAAuB,GAC/D,GAAIkJ,EAAa,CACb,IAAIpN,EAAI,IAAI2D,EAAUyJ,GACtBE,EAAmB,IAAMrM,EAAcjB,EAAEJ,EAAGI,EAAEH,EAAGG,EAAEF,EAAGE,EAAEb,EAC3D,CACD,MAAO,8CAA8CG,OAAO4E,EAAc,kBAAkB5E,OAAO+N,EAAY,iBAAiB/N,OAAOgO,EAAkB,IAC7J,EFCA7C,EAAapJ,WAAaA,EAC1BoJ,EAAa/I,oBAAsBA,EACnC+I,EAAa9I,eAAiBA"}