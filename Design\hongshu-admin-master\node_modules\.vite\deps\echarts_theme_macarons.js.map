{"version": 3, "sources": ["../../echarts/lib/echarts.js", "../../echarts/theme/macarons.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport * from './export/core.js';\nimport { use } from './extension.js';\nimport { init } from './core/echarts.js';\nimport { install as CanvasRenderer } from './renderer/installCanvasRenderer.js';\nimport { install as DatasetComponent } from './component/dataset/install.js'; // Default to have canvas renderer and dataset for compitatble reason.\n\nuse([CanvasRenderer, DatasetComponent]); // TODO: Compatitable with the following code\n// import echarts from 'echarts/lib/echarts.js'\n\nexport default {\n  init: function () {\n    if (process.env.NODE_ENV !== 'production') {\n      /* eslint-disable-next-line */\n      console.error(\"\\\"import echarts from 'echarts/lib/echarts.js'\\\" is not supported anymore. Use \\\"import * as echarts from 'echarts/lib/echarts.js'\\\" instead;\");\n    } // @ts-ignore\n\n\n    return init.apply(null, arguments);\n  }\n}; // Import label layout by default.\n// TODO remove\n\nimport { installLabelLayout } from './label/installLabelLayout.js';\nuse(installLabelLayout);", "/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\r\n\r\n(function(root, factory) {\r\n    if (typeof define === 'function' && define.amd) {\r\n        // AMD. Register as an anonymous module.\r\n        define(['exports', 'echarts'], factory);\r\n    } else if (\r\n        typeof exports === 'object' &&\r\n        typeof exports.nodeName !== 'string'\r\n    ) {\r\n        // CommonJS\r\n        factory(exports, require('echarts/lib/echarts'));\r\n    } else {\r\n        // Browser globals\r\n        factory({}, root.echarts);\r\n    }\r\n})(this, function(exports, echarts) {\r\n    var log = function(msg) {\r\n        if (typeof console !== 'undefined') {\r\n            console && console.error && console.error(msg);\r\n        }\r\n    };\r\n    if (!echarts) {\r\n        log('ECharts is not Loaded');\r\n        return;\r\n    }\r\n\r\n    var colorPalette = [\r\n        '#2ec7c9',\r\n        '#b6a2de',\r\n        '#5ab1ef',\r\n        '#ffb980',\r\n        '#d87a80',\r\n        '#8d98b3',\r\n        '#e5cf0d',\r\n        '#97b552',\r\n        '#95706d',\r\n        '#dc69aa',\r\n        '#07a2a4',\r\n        '#9a7fd1',\r\n        '#588dd5',\r\n        '#f5994e',\r\n        '#c05050',\r\n        '#59678c',\r\n        '#c9ab00',\r\n        '#7eb00a',\r\n        '#6f5553',\r\n        '#c14089'\r\n    ];\r\n\r\n    var theme = {\r\n        color: colorPalette,\r\n\r\n        title: {\r\n            textStyle: {\r\n                fontWeight: 'normal',\r\n                color: '#008acd'\r\n            }\r\n        },\r\n\r\n        visualMap: {\r\n            itemWidth: 15,\r\n            color: ['#5ab1ef', '#e0ffff']\r\n        },\r\n\r\n        toolbox: {\r\n            iconStyle: {\r\n                borderColor: colorPalette[0]\r\n            }\r\n        },\r\n\r\n        tooltip: {\r\n            borderWidth: 0,\r\n            backgroundColor: 'rgba(50,50,50,0.5)',\r\n            textStyle: {\r\n                color: '#FFF'\r\n            },\r\n            axisPointer: {\r\n                type: 'line',\r\n                lineStyle: {\r\n                    color: '#008acd'\r\n                },\r\n                crossStyle: {\r\n                    color: '#008acd'\r\n                },\r\n                shadowStyle: {\r\n                    color: 'rgba(200,200,200,0.2)'\r\n                }\r\n            }\r\n        },\r\n\r\n        dataZoom: {\r\n            dataBackgroundColor: '#efefff',\r\n            fillerColor: 'rgba(182,162,222,0.2)',\r\n            handleColor: '#008acd'\r\n        },\r\n\r\n        grid: {\r\n            borderColor: '#eee'\r\n        },\r\n\r\n        categoryAxis: {\r\n            axisLine: {\r\n                lineStyle: {\r\n                    color: '#008acd'\r\n                }\r\n            },\r\n            splitLine: {\r\n                lineStyle: {\r\n                    color: ['#eee']\r\n                }\r\n            }\r\n        },\r\n\r\n        valueAxis: {\r\n            axisLine: {\r\n                lineStyle: {\r\n                    color: '#008acd'\r\n                }\r\n            },\r\n            splitArea: {\r\n                show: true,\r\n                areaStyle: {\r\n                    color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)']\r\n                }\r\n            },\r\n            splitLine: {\r\n                lineStyle: {\r\n                    color: ['#eee']\r\n                }\r\n            }\r\n        },\r\n\r\n        timeline: {\r\n            lineStyle: {\r\n                color: '#008acd'\r\n            },\r\n            controlStyle: {\r\n                color: '#008acd',\r\n                borderColor: '#008acd'\r\n            },\r\n            symbol: 'emptyCircle',\r\n            symbolSize: 3\r\n        },\r\n\r\n        line: {\r\n            smooth: true,\r\n            symbol: 'emptyCircle',\r\n            symbolSize: 3\r\n        },\r\n\r\n        candlestick: {\r\n            itemStyle: {\r\n                color: '#d87a80',\r\n                color0: '#2ec7c9'\r\n            },\r\n            lineStyle: {\r\n                width: 1,\r\n                color: '#d87a80',\r\n                color0: '#2ec7c9'\r\n            },\r\n            areaStyle: {\r\n                color: '#2ec7c9',\r\n                color0: '#b6a2de'\r\n            }\r\n        },\r\n\r\n        scatter: {\r\n            symbol: 'circle',\r\n            symbolSize: 4\r\n        },\r\n\r\n        map: {\r\n            itemStyle: {\r\n                color: '#ddd'\r\n            },\r\n            areaStyle: {\r\n                color: '#fe994e'\r\n            },\r\n            label: {\r\n                color: '#d87a80'\r\n            }\r\n        },\r\n\r\n        graph: {\r\n            itemStyle: {\r\n                color: '#d87a80'\r\n            },\r\n            linkStyle: {\r\n                color: '#2ec7c9'\r\n            }\r\n        },\r\n\r\n        gauge: {\r\n            axisLine: {\r\n                lineStyle: {\r\n                    color: [\r\n                        [0.2, '#2ec7c9'],\r\n                        [0.8, '#5ab1ef'],\r\n                        [1, '#d87a80']\r\n                    ],\r\n                    width: 10\r\n                }\r\n            },\r\n            axisTick: {\r\n                splitNumber: 10,\r\n                length: 15,\r\n                lineStyle: {\r\n                    color: 'auto'\r\n                }\r\n            },\r\n            splitLine: {\r\n                length: 22,\r\n                lineStyle: {\r\n                    color: 'auto'\r\n                }\r\n            },\r\n            pointer: {\r\n                width: 5\r\n            }\r\n        }\r\n    };\r\n\r\n    echarts.registerTheme('macarons', theme);\r\n});\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAoDO;AApDP,IAAAC,gBAAA;AAAA;AA2CA;AACA;AACA;AACA;AACA;AAkBA;AAhBA,QAAI,CAAC,SAAgBC,QAAgB,CAAC;AAGtC,IAAO,kBAAQ;AAAA,MACb,MAAM,WAAY;AAChB,YAAI,MAAuC;AAEzC,kBAAQ,MAAM,2IAA+I;AAAA,QAC/J;AAGA,eAAO,KAAK,MAAM,MAAM,SAAS;AAAA,MACnC;AAAA,IACF;AAIA,QAAI,kBAAkB;AAAA;AAAA;;;AClEtB;AAAA;AAmBA,KAAC,SAAS,MAAM,SAAS;AACrB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAE5C,eAAO,CAAC,WAAW,SAAS,GAAG,OAAO;AAAA,MAC1C,WACI,OAAO,YAAY,YACnB,OAAO,QAAQ,aAAa,UAC9B;AAEE,gBAAQ,SAAS,gDAA8B;AAAA,MACnD,OAAO;AAEH,gBAAQ,CAAC,GAAG,KAAK,OAAO;AAAA,MAC5B;AAAA,IACJ,GAAG,SAAM,SAASC,UAAS,SAAS;AAChC,UAAI,MAAM,SAAS,KAAK;AACpB,YAAI,OAAO,YAAY,aAAa;AAChC,qBAAW,QAAQ,SAAS,QAAQ,MAAM,GAAG;AAAA,QACjD;AAAA,MACJ;AACA,UAAI,CAAC,SAAS;AACV,YAAI,uBAAuB;AAC3B;AAAA,MACJ;AAEA,UAAI,eAAe;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAEA,UAAI,QAAQ;AAAA,QACR,OAAO;AAAA,QAEP,OAAO;AAAA,UACH,WAAW;AAAA,YACP,YAAY;AAAA,YACZ,OAAO;AAAA,UACX;AAAA,QACJ;AAAA,QAEA,WAAW;AAAA,UACP,WAAW;AAAA,UACX,OAAO,CAAC,WAAW,SAAS;AAAA,QAChC;AAAA,QAEA,SAAS;AAAA,UACL,WAAW;AAAA,YACP,aAAa,aAAa,CAAC;AAAA,UAC/B;AAAA,QACJ;AAAA,QAEA,SAAS;AAAA,UACL,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,WAAW;AAAA,YACP,OAAO;AAAA,UACX;AAAA,UACA,aAAa;AAAA,YACT,MAAM;AAAA,YACN,WAAW;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YACA,YAAY;AAAA,cACR,OAAO;AAAA,YACX;AAAA,YACA,aAAa;AAAA,cACT,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AAAA,QAEA,UAAU;AAAA,UACN,qBAAqB;AAAA,UACrB,aAAa;AAAA,UACb,aAAa;AAAA,QACjB;AAAA,QAEA,MAAM;AAAA,UACF,aAAa;AAAA,QACjB;AAAA,QAEA,cAAc;AAAA,UACV,UAAU;AAAA,YACN,WAAW;AAAA,cACP,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,WAAW;AAAA,YACP,WAAW;AAAA,cACP,OAAO,CAAC,MAAM;AAAA,YAClB;AAAA,UACJ;AAAA,QACJ;AAAA,QAEA,WAAW;AAAA,UACP,UAAU;AAAA,YACN,WAAW;AAAA,cACP,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,WAAW;AAAA,YACP,MAAM;AAAA,YACN,WAAW;AAAA,cACP,OAAO,CAAC,yBAAyB,uBAAuB;AAAA,YAC5D;AAAA,UACJ;AAAA,UACA,WAAW;AAAA,YACP,WAAW;AAAA,cACP,OAAO,CAAC,MAAM;AAAA,YAClB;AAAA,UACJ;AAAA,QACJ;AAAA,QAEA,UAAU;AAAA,UACN,WAAW;AAAA,YACP,OAAO;AAAA,UACX;AAAA,UACA,cAAc;AAAA,YACV,OAAO;AAAA,YACP,aAAa;AAAA,UACjB;AAAA,UACA,QAAQ;AAAA,UACR,YAAY;AAAA,QAChB;AAAA,QAEA,MAAM;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,YAAY;AAAA,QAChB;AAAA,QAEA,aAAa;AAAA,UACT,WAAW;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,UACZ;AAAA,UACA,WAAW;AAAA,YACP,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,UACZ;AAAA,UACA,WAAW;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,UACZ;AAAA,QACJ;AAAA,QAEA,SAAS;AAAA,UACL,QAAQ;AAAA,UACR,YAAY;AAAA,QAChB;AAAA,QAEA,KAAK;AAAA,UACD,WAAW;AAAA,YACP,OAAO;AAAA,UACX;AAAA,UACA,WAAW;AAAA,YACP,OAAO;AAAA,UACX;AAAA,UACA,OAAO;AAAA,YACH,OAAO;AAAA,UACX;AAAA,QACJ;AAAA,QAEA,OAAO;AAAA,UACH,WAAW;AAAA,YACP,OAAO;AAAA,UACX;AAAA,UACA,WAAW;AAAA,YACP,OAAO;AAAA,UACX;AAAA,QACJ;AAAA,QAEA,OAAO;AAAA,UACH,UAAU;AAAA,YACN,WAAW;AAAA,cACP,OAAO;AAAA,gBACH,CAAC,KAAK,SAAS;AAAA,gBACf,CAAC,KAAK,SAAS;AAAA,gBACf,CAAC,GAAG,SAAS;AAAA,cACjB;AAAA,cACA,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,UAAU;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,YACR,WAAW;AAAA,cACP,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,WAAW;AAAA,YACP,QAAQ;AAAA,YACR,WAAW;AAAA,cACP,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,SAAS;AAAA,YACL,OAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AAEA,cAAQ,cAAc,YAAY,KAAK;AAAA,IAC3C,CAAC;AAAA;AAAA;", "names": ["Component_default", "util_exports", "init_echarts", "install", "exports"]}