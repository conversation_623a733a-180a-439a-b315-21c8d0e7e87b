import {
  Axis_default,
  Chart_default,
  Component_default,
  Component_default2,
  Model_default,
  PRIORITY,
  SeriesData_default,
  Series_default,
  brushSingle,
  color_exports,
  connect,
  dataTool,
  dependencies,
  disConnect,
  disconnect,
  dispose,
  env_default,
  extendChartView,
  extendComponentModel,
  extendComponentView,
  extendSeriesModel,
  format_exports,
  getCoordinateSystemDimensions,
  getInstanceByDom,
  getInstanceById,
  getMap,
  graphic_exports2 as graphic_exports,
  helper_exports,
  init,
  init_core,
  init_echarts,
  init_extension,
  init_install,
  init_installCanvasRenderer,
  init_installLabelLayout,
  install,
  install2,
  installLabelLayout,
  matrix_exports,
  number_exports,
  parseGeoJSON,
  registerAction,
  registerCoordinateSystem,
  registerLayout,
  registerLoading,
  registerLocale,
  registerMap,
  registerPostInit,
  registerPostUpdate,
  registerPreprocessor,
  registerProcessor,
  registerTheme,
  registerTransform,
  registerUpdateLifecycle,
  registerVisual,
  setCanvasCreator,
  setPlatformAPI,
  throttle,
  time_exports,
  use,
  util_exports,
  util_exports2,
  vector_exports,
  version,
  zrender_exports
} from "./chunk-GW74MOTF.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS
} from "./chunk-CEQRFMJQ.js";

// node_modules/echarts/lib/echarts.js
var echarts_exports = {};
__export(echarts_exports, {
  Axis: () => Axis_default,
  ChartView: () => Chart_default,
  ComponentModel: () => Component_default,
  ComponentView: () => Component_default2,
  List: () => SeriesData_default,
  Model: () => Model_default,
  PRIORITY: () => PRIORITY,
  SeriesModel: () => Series_default,
  color: () => color_exports,
  connect: () => connect,
  dataTool: () => dataTool,
  default: () => echarts_default,
  dependencies: () => dependencies,
  disConnect: () => disConnect,
  disconnect: () => disconnect,
  dispose: () => dispose,
  env: () => env_default,
  extendChartView: () => extendChartView,
  extendComponentModel: () => extendComponentModel,
  extendComponentView: () => extendComponentView,
  extendSeriesModel: () => extendSeriesModel,
  format: () => format_exports,
  getCoordinateSystemDimensions: () => getCoordinateSystemDimensions,
  getInstanceByDom: () => getInstanceByDom,
  getInstanceById: () => getInstanceById,
  getMap: () => getMap,
  graphic: () => graphic_exports,
  helper: () => helper_exports,
  init: () => init,
  innerDrawElementOnCanvas: () => brushSingle,
  matrix: () => matrix_exports,
  number: () => number_exports,
  parseGeoJSON: () => parseGeoJSON,
  parseGeoJson: () => parseGeoJSON,
  registerAction: () => registerAction,
  registerCoordinateSystem: () => registerCoordinateSystem,
  registerLayout: () => registerLayout,
  registerLoading: () => registerLoading,
  registerLocale: () => registerLocale,
  registerMap: () => registerMap,
  registerPostInit: () => registerPostInit,
  registerPostUpdate: () => registerPostUpdate,
  registerPreprocessor: () => registerPreprocessor,
  registerProcessor: () => registerProcessor,
  registerTheme: () => registerTheme,
  registerTransform: () => registerTransform,
  registerUpdateLifecycle: () => registerUpdateLifecycle,
  registerVisual: () => registerVisual,
  setCanvasCreator: () => setCanvasCreator,
  setPlatformAPI: () => setPlatformAPI,
  throttle: () => throttle,
  time: () => time_exports,
  use: () => use,
  util: () => util_exports2,
  vector: () => vector_exports,
  version: () => version,
  zrUtil: () => util_exports,
  zrender: () => zrender_exports
});
var echarts_default;
var init_echarts2 = __esm({
  "node_modules/echarts/lib/echarts.js"() {
    init_core();
    init_extension();
    init_echarts();
    init_installCanvasRenderer();
    init_install();
    init_installLabelLayout();
    use([install, install2]);
    echarts_default = {
      init: function() {
        if (true) {
          console.error(`"import echarts from 'echarts/lib/echarts.js'" is not supported anymore. Use "import * as echarts from 'echarts/lib/echarts.js'" instead;`);
        }
        return init.apply(null, arguments);
      }
    };
    use(installLabelLayout);
  }
});

// node_modules/echarts/theme/macarons.js
var require_macarons = __commonJS({
  "node_modules/echarts/theme/macarons.js"(exports) {
    (function(root, factory) {
      if (typeof define === "function" && define.amd) {
        define(["exports", "echarts"], factory);
      } else if (typeof exports === "object" && typeof exports.nodeName !== "string") {
        factory(exports, (init_echarts2(), __toCommonJS(echarts_exports)));
      } else {
        factory({}, root.echarts);
      }
    })(exports, function(exports2, echarts) {
      var log = function(msg) {
        if (typeof console !== "undefined") {
          console && console.error && console.error(msg);
        }
      };
      if (!echarts) {
        log("ECharts is not Loaded");
        return;
      }
      var colorPalette = [
        "#2ec7c9",
        "#b6a2de",
        "#5ab1ef",
        "#ffb980",
        "#d87a80",
        "#8d98b3",
        "#e5cf0d",
        "#97b552",
        "#95706d",
        "#dc69aa",
        "#07a2a4",
        "#9a7fd1",
        "#588dd5",
        "#f5994e",
        "#c05050",
        "#59678c",
        "#c9ab00",
        "#7eb00a",
        "#6f5553",
        "#c14089"
      ];
      var theme = {
        color: colorPalette,
        title: {
          textStyle: {
            fontWeight: "normal",
            color: "#008acd"
          }
        },
        visualMap: {
          itemWidth: 15,
          color: ["#5ab1ef", "#e0ffff"]
        },
        toolbox: {
          iconStyle: {
            borderColor: colorPalette[0]
          }
        },
        tooltip: {
          borderWidth: 0,
          backgroundColor: "rgba(50,50,50,0.5)",
          textStyle: {
            color: "#FFF"
          },
          axisPointer: {
            type: "line",
            lineStyle: {
              color: "#008acd"
            },
            crossStyle: {
              color: "#008acd"
            },
            shadowStyle: {
              color: "rgba(200,200,200,0.2)"
            }
          }
        },
        dataZoom: {
          dataBackgroundColor: "#efefff",
          fillerColor: "rgba(182,162,222,0.2)",
          handleColor: "#008acd"
        },
        grid: {
          borderColor: "#eee"
        },
        categoryAxis: {
          axisLine: {
            lineStyle: {
              color: "#008acd"
            }
          },
          splitLine: {
            lineStyle: {
              color: ["#eee"]
            }
          }
        },
        valueAxis: {
          axisLine: {
            lineStyle: {
              color: "#008acd"
            }
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: ["rgba(250,250,250,0.1)", "rgba(200,200,200,0.1)"]
            }
          },
          splitLine: {
            lineStyle: {
              color: ["#eee"]
            }
          }
        },
        timeline: {
          lineStyle: {
            color: "#008acd"
          },
          controlStyle: {
            color: "#008acd",
            borderColor: "#008acd"
          },
          symbol: "emptyCircle",
          symbolSize: 3
        },
        line: {
          smooth: true,
          symbol: "emptyCircle",
          symbolSize: 3
        },
        candlestick: {
          itemStyle: {
            color: "#d87a80",
            color0: "#2ec7c9"
          },
          lineStyle: {
            width: 1,
            color: "#d87a80",
            color0: "#2ec7c9"
          },
          areaStyle: {
            color: "#2ec7c9",
            color0: "#b6a2de"
          }
        },
        scatter: {
          symbol: "circle",
          symbolSize: 4
        },
        map: {
          itemStyle: {
            color: "#ddd"
          },
          areaStyle: {
            color: "#fe994e"
          },
          label: {
            color: "#d87a80"
          }
        },
        graph: {
          itemStyle: {
            color: "#d87a80"
          },
          linkStyle: {
            color: "#2ec7c9"
          }
        },
        gauge: {
          axisLine: {
            lineStyle: {
              color: [
                [0.2, "#2ec7c9"],
                [0.8, "#5ab1ef"],
                [1, "#d87a80"]
              ],
              width: 10
            }
          },
          axisTick: {
            splitNumber: 10,
            length: 15,
            lineStyle: {
              color: "auto"
            }
          },
          splitLine: {
            length: 22,
            lineStyle: {
              color: "auto"
            }
          },
          pointer: {
            width: 5
          }
        }
      };
      echarts.registerTheme("macarons", theme);
    });
  }
});
export default require_macarons();
//# sourceMappingURL=echarts_theme_macarons.js.map
