{"version": 3, "sources": ["../../fuse.js/dist/fuse.esm.js"], "sourcesContent": ["/**\n * Fuse.js v6.6.2 - Lightweight fuzzy-search (http://fusejs.io)\n *\n * Copyright (c) 2022 Kiro Risk (http://kiro.me)\n * All Rights Reserved. Apache Software License 2.0\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nfunction isArray(value) {\n  return !Array.isArray\n    ? getTag(value) === '[object Array]'\n    : Array.isArray(value)\n}\n\n// Adapted from: https://github.com/lodash/lodash/blob/master/.internal/baseToString.js\nconst INFINITY = 1 / 0;\nfunction baseToString(value) {\n  // Exit early for strings to avoid a performance hit in some environments.\n  if (typeof value == 'string') {\n    return value\n  }\n  let result = value + '';\n  return result == '0' && 1 / value == -INFINITY ? '-0' : result\n}\n\nfunction toString(value) {\n  return value == null ? '' : baseToString(value)\n}\n\nfunction isString(value) {\n  return typeof value === 'string'\n}\n\nfunction isNumber(value) {\n  return typeof value === 'number'\n}\n\n// Adapted from: https://github.com/lodash/lodash/blob/master/isBoolean.js\nfunction isBoolean(value) {\n  return (\n    value === true ||\n    value === false ||\n    (isObjectLike(value) && getTag(value) == '[object Boolean]')\n  )\n}\n\nfunction isObject(value) {\n  return typeof value === 'object'\n}\n\n// Checks if `value` is object-like.\nfunction isObjectLike(value) {\n  return isObject(value) && value !== null\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null\n}\n\nfunction isBlank(value) {\n  return !value.trim().length\n}\n\n// Gets the `toStringTag` of `value`.\n// Adapted from: https://github.com/lodash/lodash/blob/master/.internal/getTag.js\nfunction getTag(value) {\n  return value == null\n    ? value === undefined\n      ? '[object Undefined]'\n      : '[object Null]'\n    : Object.prototype.toString.call(value)\n}\n\nconst EXTENDED_SEARCH_UNAVAILABLE = 'Extended search is not available';\n\nconst INCORRECT_INDEX_TYPE = \"Incorrect 'index' type\";\n\nconst LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY = (key) =>\n  `Invalid value for key ${key}`;\n\nconst PATTERN_LENGTH_TOO_LARGE = (max) =>\n  `Pattern length exceeds max of ${max}.`;\n\nconst MISSING_KEY_PROPERTY = (name) => `Missing ${name} property in key`;\n\nconst INVALID_KEY_WEIGHT_VALUE = (key) =>\n  `Property 'weight' in key '${key}' must be a positive integer`;\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nclass KeyStore {\n  constructor(keys) {\n    this._keys = [];\n    this._keyMap = {};\n\n    let totalWeight = 0;\n\n    keys.forEach((key) => {\n      let obj = createKey(key);\n\n      totalWeight += obj.weight;\n\n      this._keys.push(obj);\n      this._keyMap[obj.id] = obj;\n\n      totalWeight += obj.weight;\n    });\n\n    // Normalize weights so that their sum is equal to 1\n    this._keys.forEach((key) => {\n      key.weight /= totalWeight;\n    });\n  }\n  get(keyId) {\n    return this._keyMap[keyId]\n  }\n  keys() {\n    return this._keys\n  }\n  toJSON() {\n    return JSON.stringify(this._keys)\n  }\n}\n\nfunction createKey(key) {\n  let path = null;\n  let id = null;\n  let src = null;\n  let weight = 1;\n  let getFn = null;\n\n  if (isString(key) || isArray(key)) {\n    src = key;\n    path = createKeyPath(key);\n    id = createKeyId(key);\n  } else {\n    if (!hasOwn.call(key, 'name')) {\n      throw new Error(MISSING_KEY_PROPERTY('name'))\n    }\n\n    const name = key.name;\n    src = name;\n\n    if (hasOwn.call(key, 'weight')) {\n      weight = key.weight;\n\n      if (weight <= 0) {\n        throw new Error(INVALID_KEY_WEIGHT_VALUE(name))\n      }\n    }\n\n    path = createKeyPath(name);\n    id = createKeyId(name);\n    getFn = key.getFn;\n  }\n\n  return { path, id, weight, src, getFn }\n}\n\nfunction createKeyPath(key) {\n  return isArray(key) ? key : key.split('.')\n}\n\nfunction createKeyId(key) {\n  return isArray(key) ? key.join('.') : key\n}\n\nfunction get(obj, path) {\n  let list = [];\n  let arr = false;\n\n  const deepGet = (obj, path, index) => {\n    if (!isDefined(obj)) {\n      return\n    }\n    if (!path[index]) {\n      // If there's no path left, we've arrived at the object we care about.\n      list.push(obj);\n    } else {\n      let key = path[index];\n\n      const value = obj[key];\n\n      if (!isDefined(value)) {\n        return\n      }\n\n      // If we're at the last value in the path, and if it's a string/number/bool,\n      // add it to the list\n      if (\n        index === path.length - 1 &&\n        (isString(value) || isNumber(value) || isBoolean(value))\n      ) {\n        list.push(toString(value));\n      } else if (isArray(value)) {\n        arr = true;\n        // Search each item in the array.\n        for (let i = 0, len = value.length; i < len; i += 1) {\n          deepGet(value[i], path, index + 1);\n        }\n      } else if (path.length) {\n        // An object. Recurse further.\n        deepGet(value, path, index + 1);\n      }\n    }\n  };\n\n  // Backwards compatibility (since path used to be a string)\n  deepGet(obj, isString(path) ? path.split('.') : path, 0);\n\n  return arr ? list : list[0]\n}\n\nconst MatchOptions = {\n  // Whether the matches should be included in the result set. When `true`, each record in the result\n  // set will include the indices of the matched characters.\n  // These can consequently be used for highlighting purposes.\n  includeMatches: false,\n  // When `true`, the matching function will continue to the end of a search pattern even if\n  // a perfect match has already been located in the string.\n  findAllMatches: false,\n  // Minimum number of characters that must be matched before a result is considered a match\n  minMatchCharLength: 1\n};\n\nconst BasicOptions = {\n  // When `true`, the algorithm continues searching to the end of the input even if a perfect\n  // match is found before the end of the same input.\n  isCaseSensitive: false,\n  // When true, the matching function will continue to the end of a search pattern even if\n  includeScore: false,\n  // List of properties that will be searched. This also supports nested properties.\n  keys: [],\n  // Whether to sort the result list, by score\n  shouldSort: true,\n  // Default sort function: sort by ascending score, ascending index\n  sortFn: (a, b) =>\n    a.score === b.score ? (a.idx < b.idx ? -1 : 1) : a.score < b.score ? -1 : 1\n};\n\nconst FuzzyOptions = {\n  // Approximately where in the text is the pattern expected to be found?\n  location: 0,\n  // At what point does the match algorithm give up. A threshold of '0.0' requires a perfect match\n  // (of both letters and location), a threshold of '1.0' would match anything.\n  threshold: 0.6,\n  // Determines how close the match must be to the fuzzy location (specified above).\n  // An exact letter match which is 'distance' characters away from the fuzzy location\n  // would score as a complete mismatch. A distance of '0' requires the match be at\n  // the exact location specified, a threshold of '1000' would require a perfect match\n  // to be within 800 characters of the fuzzy location to be found using a 0.8 threshold.\n  distance: 100\n};\n\nconst AdvancedOptions = {\n  // When `true`, it enables the use of unix-like search commands\n  useExtendedSearch: false,\n  // The get function to use when fetching an object's properties.\n  // The default will search nested paths *ie foo.bar.baz*\n  getFn: get,\n  // When `true`, search will ignore `location` and `distance`, so it won't matter\n  // where in the string the pattern appears.\n  // More info: https://fusejs.io/concepts/scoring-theory.html#fuzziness-score\n  ignoreLocation: false,\n  // When `true`, the calculation for the relevance score (used for sorting) will\n  // ignore the field-length norm.\n  // More info: https://fusejs.io/concepts/scoring-theory.html#field-length-norm\n  ignoreFieldNorm: false,\n  // The weight to determine how much field length norm effects scoring.\n  fieldNormWeight: 1\n};\n\nvar Config = {\n  ...BasicOptions,\n  ...MatchOptions,\n  ...FuzzyOptions,\n  ...AdvancedOptions\n};\n\nconst SPACE = /[^ ]+/g;\n\n// Field-length norm: the shorter the field, the higher the weight.\n// Set to 3 decimals to reduce index size.\nfunction norm(weight = 1, mantissa = 3) {\n  const cache = new Map();\n  const m = Math.pow(10, mantissa);\n\n  return {\n    get(value) {\n      const numTokens = value.match(SPACE).length;\n\n      if (cache.has(numTokens)) {\n        return cache.get(numTokens)\n      }\n\n      // Default function is 1/sqrt(x), weight makes that variable\n      const norm = 1 / Math.pow(numTokens, 0.5 * weight);\n\n      // In place of `toFixed(mantissa)`, for faster computation\n      const n = parseFloat(Math.round(norm * m) / m);\n\n      cache.set(numTokens, n);\n\n      return n\n    },\n    clear() {\n      cache.clear();\n    }\n  }\n}\n\nclass FuseIndex {\n  constructor({\n    getFn = Config.getFn,\n    fieldNormWeight = Config.fieldNormWeight\n  } = {}) {\n    this.norm = norm(fieldNormWeight, 3);\n    this.getFn = getFn;\n    this.isCreated = false;\n\n    this.setIndexRecords();\n  }\n  setSources(docs = []) {\n    this.docs = docs;\n  }\n  setIndexRecords(records = []) {\n    this.records = records;\n  }\n  setKeys(keys = []) {\n    this.keys = keys;\n    this._keysMap = {};\n    keys.forEach((key, idx) => {\n      this._keysMap[key.id] = idx;\n    });\n  }\n  create() {\n    if (this.isCreated || !this.docs.length) {\n      return\n    }\n\n    this.isCreated = true;\n\n    // List is Array<String>\n    if (isString(this.docs[0])) {\n      this.docs.forEach((doc, docIndex) => {\n        this._addString(doc, docIndex);\n      });\n    } else {\n      // List is Array<Object>\n      this.docs.forEach((doc, docIndex) => {\n        this._addObject(doc, docIndex);\n      });\n    }\n\n    this.norm.clear();\n  }\n  // Adds a doc to the end of the index\n  add(doc) {\n    const idx = this.size();\n\n    if (isString(doc)) {\n      this._addString(doc, idx);\n    } else {\n      this._addObject(doc, idx);\n    }\n  }\n  // Removes the doc at the specified index of the index\n  removeAt(idx) {\n    this.records.splice(idx, 1);\n\n    // Change ref index of every subsquent doc\n    for (let i = idx, len = this.size(); i < len; i += 1) {\n      this.records[i].i -= 1;\n    }\n  }\n  getValueForItemAtKeyId(item, keyId) {\n    return item[this._keysMap[keyId]]\n  }\n  size() {\n    return this.records.length\n  }\n  _addString(doc, docIndex) {\n    if (!isDefined(doc) || isBlank(doc)) {\n      return\n    }\n\n    let record = {\n      v: doc,\n      i: docIndex,\n      n: this.norm.get(doc)\n    };\n\n    this.records.push(record);\n  }\n  _addObject(doc, docIndex) {\n    let record = { i: docIndex, $: {} };\n\n    // Iterate over every key (i.e, path), and fetch the value at that key\n    this.keys.forEach((key, keyIndex) => {\n      let value = key.getFn ? key.getFn(doc) : this.getFn(doc, key.path);\n\n      if (!isDefined(value)) {\n        return\n      }\n\n      if (isArray(value)) {\n        let subRecords = [];\n        const stack = [{ nestedArrIndex: -1, value }];\n\n        while (stack.length) {\n          const { nestedArrIndex, value } = stack.pop();\n\n          if (!isDefined(value)) {\n            continue\n          }\n\n          if (isString(value) && !isBlank(value)) {\n            let subRecord = {\n              v: value,\n              i: nestedArrIndex,\n              n: this.norm.get(value)\n            };\n\n            subRecords.push(subRecord);\n          } else if (isArray(value)) {\n            value.forEach((item, k) => {\n              stack.push({\n                nestedArrIndex: k,\n                value: item\n              });\n            });\n          } else ;\n        }\n        record.$[keyIndex] = subRecords;\n      } else if (isString(value) && !isBlank(value)) {\n        let subRecord = {\n          v: value,\n          n: this.norm.get(value)\n        };\n\n        record.$[keyIndex] = subRecord;\n      }\n    });\n\n    this.records.push(record);\n  }\n  toJSON() {\n    return {\n      keys: this.keys,\n      records: this.records\n    }\n  }\n}\n\nfunction createIndex(\n  keys,\n  docs,\n  { getFn = Config.getFn, fieldNormWeight = Config.fieldNormWeight } = {}\n) {\n  const myIndex = new FuseIndex({ getFn, fieldNormWeight });\n  myIndex.setKeys(keys.map(createKey));\n  myIndex.setSources(docs);\n  myIndex.create();\n  return myIndex\n}\n\nfunction parseIndex(\n  data,\n  { getFn = Config.getFn, fieldNormWeight = Config.fieldNormWeight } = {}\n) {\n  const { keys, records } = data;\n  const myIndex = new FuseIndex({ getFn, fieldNormWeight });\n  myIndex.setKeys(keys);\n  myIndex.setIndexRecords(records);\n  return myIndex\n}\n\nfunction computeScore$1(\n  pattern,\n  {\n    errors = 0,\n    currentLocation = 0,\n    expectedLocation = 0,\n    distance = Config.distance,\n    ignoreLocation = Config.ignoreLocation\n  } = {}\n) {\n  const accuracy = errors / pattern.length;\n\n  if (ignoreLocation) {\n    return accuracy\n  }\n\n  const proximity = Math.abs(expectedLocation - currentLocation);\n\n  if (!distance) {\n    // Dodge divide by zero error.\n    return proximity ? 1.0 : accuracy\n  }\n\n  return accuracy + proximity / distance\n}\n\nfunction convertMaskToIndices(\n  matchmask = [],\n  minMatchCharLength = Config.minMatchCharLength\n) {\n  let indices = [];\n  let start = -1;\n  let end = -1;\n  let i = 0;\n\n  for (let len = matchmask.length; i < len; i += 1) {\n    let match = matchmask[i];\n    if (match && start === -1) {\n      start = i;\n    } else if (!match && start !== -1) {\n      end = i - 1;\n      if (end - start + 1 >= minMatchCharLength) {\n        indices.push([start, end]);\n      }\n      start = -1;\n    }\n  }\n\n  // (i-1 - start) + 1 => i - start\n  if (matchmask[i - 1] && i - start >= minMatchCharLength) {\n    indices.push([start, i - 1]);\n  }\n\n  return indices\n}\n\n// Machine word size\nconst MAX_BITS = 32;\n\nfunction search(\n  text,\n  pattern,\n  patternAlphabet,\n  {\n    location = Config.location,\n    distance = Config.distance,\n    threshold = Config.threshold,\n    findAllMatches = Config.findAllMatches,\n    minMatchCharLength = Config.minMatchCharLength,\n    includeMatches = Config.includeMatches,\n    ignoreLocation = Config.ignoreLocation\n  } = {}\n) {\n  if (pattern.length > MAX_BITS) {\n    throw new Error(PATTERN_LENGTH_TOO_LARGE(MAX_BITS))\n  }\n\n  const patternLen = pattern.length;\n  // Set starting location at beginning text and initialize the alphabet.\n  const textLen = text.length;\n  // Handle the case when location > text.length\n  const expectedLocation = Math.max(0, Math.min(location, textLen));\n  // Highest score beyond which we give up.\n  let currentThreshold = threshold;\n  // Is there a nearby exact match? (speedup)\n  let bestLocation = expectedLocation;\n\n  // Performance: only computer matches when the minMatchCharLength > 1\n  // OR if `includeMatches` is true.\n  const computeMatches = minMatchCharLength > 1 || includeMatches;\n  // A mask of the matches, used for building the indices\n  const matchMask = computeMatches ? Array(textLen) : [];\n\n  let index;\n\n  // Get all exact matches, here for speed up\n  while ((index = text.indexOf(pattern, bestLocation)) > -1) {\n    let score = computeScore$1(pattern, {\n      currentLocation: index,\n      expectedLocation,\n      distance,\n      ignoreLocation\n    });\n\n    currentThreshold = Math.min(score, currentThreshold);\n    bestLocation = index + patternLen;\n\n    if (computeMatches) {\n      let i = 0;\n      while (i < patternLen) {\n        matchMask[index + i] = 1;\n        i += 1;\n      }\n    }\n  }\n\n  // Reset the best location\n  bestLocation = -1;\n\n  let lastBitArr = [];\n  let finalScore = 1;\n  let binMax = patternLen + textLen;\n\n  const mask = 1 << (patternLen - 1);\n\n  for (let i = 0; i < patternLen; i += 1) {\n    // Scan for the best match; each iteration allows for one more error.\n    // Run a binary search to determine how far from the match location we can stray\n    // at this error level.\n    let binMin = 0;\n    let binMid = binMax;\n\n    while (binMin < binMid) {\n      const score = computeScore$1(pattern, {\n        errors: i,\n        currentLocation: expectedLocation + binMid,\n        expectedLocation,\n        distance,\n        ignoreLocation\n      });\n\n      if (score <= currentThreshold) {\n        binMin = binMid;\n      } else {\n        binMax = binMid;\n      }\n\n      binMid = Math.floor((binMax - binMin) / 2 + binMin);\n    }\n\n    // Use the result from this iteration as the maximum for the next.\n    binMax = binMid;\n\n    let start = Math.max(1, expectedLocation - binMid + 1);\n    let finish = findAllMatches\n      ? textLen\n      : Math.min(expectedLocation + binMid, textLen) + patternLen;\n\n    // Initialize the bit array\n    let bitArr = Array(finish + 2);\n\n    bitArr[finish + 1] = (1 << i) - 1;\n\n    for (let j = finish; j >= start; j -= 1) {\n      let currentLocation = j - 1;\n      let charMatch = patternAlphabet[text.charAt(currentLocation)];\n\n      if (computeMatches) {\n        // Speed up: quick bool to int conversion (i.e, `charMatch ? 1 : 0`)\n        matchMask[currentLocation] = +!!charMatch;\n      }\n\n      // First pass: exact match\n      bitArr[j] = ((bitArr[j + 1] << 1) | 1) & charMatch;\n\n      // Subsequent passes: fuzzy match\n      if (i) {\n        bitArr[j] |=\n          ((lastBitArr[j + 1] | lastBitArr[j]) << 1) | 1 | lastBitArr[j + 1];\n      }\n\n      if (bitArr[j] & mask) {\n        finalScore = computeScore$1(pattern, {\n          errors: i,\n          currentLocation,\n          expectedLocation,\n          distance,\n          ignoreLocation\n        });\n\n        // This match will almost certainly be better than any existing match.\n        // But check anyway.\n        if (finalScore <= currentThreshold) {\n          // Indeed it is\n          currentThreshold = finalScore;\n          bestLocation = currentLocation;\n\n          // Already passed `loc`, downhill from here on in.\n          if (bestLocation <= expectedLocation) {\n            break\n          }\n\n          // When passing `bestLocation`, don't exceed our current distance from `expectedLocation`.\n          start = Math.max(1, 2 * expectedLocation - bestLocation);\n        }\n      }\n    }\n\n    // No hope for a (better) match at greater error levels.\n    const score = computeScore$1(pattern, {\n      errors: i + 1,\n      currentLocation: expectedLocation,\n      expectedLocation,\n      distance,\n      ignoreLocation\n    });\n\n    if (score > currentThreshold) {\n      break\n    }\n\n    lastBitArr = bitArr;\n  }\n\n  const result = {\n    isMatch: bestLocation >= 0,\n    // Count exact matches (those with a score of 0) to be \"almost\" exact\n    score: Math.max(0.001, finalScore)\n  };\n\n  if (computeMatches) {\n    const indices = convertMaskToIndices(matchMask, minMatchCharLength);\n    if (!indices.length) {\n      result.isMatch = false;\n    } else if (includeMatches) {\n      result.indices = indices;\n    }\n  }\n\n  return result\n}\n\nfunction createPatternAlphabet(pattern) {\n  let mask = {};\n\n  for (let i = 0, len = pattern.length; i < len; i += 1) {\n    const char = pattern.charAt(i);\n    mask[char] = (mask[char] || 0) | (1 << (len - i - 1));\n  }\n\n  return mask\n}\n\nclass BitapSearch {\n  constructor(\n    pattern,\n    {\n      location = Config.location,\n      threshold = Config.threshold,\n      distance = Config.distance,\n      includeMatches = Config.includeMatches,\n      findAllMatches = Config.findAllMatches,\n      minMatchCharLength = Config.minMatchCharLength,\n      isCaseSensitive = Config.isCaseSensitive,\n      ignoreLocation = Config.ignoreLocation\n    } = {}\n  ) {\n    this.options = {\n      location,\n      threshold,\n      distance,\n      includeMatches,\n      findAllMatches,\n      minMatchCharLength,\n      isCaseSensitive,\n      ignoreLocation\n    };\n\n    this.pattern = isCaseSensitive ? pattern : pattern.toLowerCase();\n\n    this.chunks = [];\n\n    if (!this.pattern.length) {\n      return\n    }\n\n    const addChunk = (pattern, startIndex) => {\n      this.chunks.push({\n        pattern,\n        alphabet: createPatternAlphabet(pattern),\n        startIndex\n      });\n    };\n\n    const len = this.pattern.length;\n\n    if (len > MAX_BITS) {\n      let i = 0;\n      const remainder = len % MAX_BITS;\n      const end = len - remainder;\n\n      while (i < end) {\n        addChunk(this.pattern.substr(i, MAX_BITS), i);\n        i += MAX_BITS;\n      }\n\n      if (remainder) {\n        const startIndex = len - MAX_BITS;\n        addChunk(this.pattern.substr(startIndex), startIndex);\n      }\n    } else {\n      addChunk(this.pattern, 0);\n    }\n  }\n\n  searchIn(text) {\n    const { isCaseSensitive, includeMatches } = this.options;\n\n    if (!isCaseSensitive) {\n      text = text.toLowerCase();\n    }\n\n    // Exact match\n    if (this.pattern === text) {\n      let result = {\n        isMatch: true,\n        score: 0\n      };\n\n      if (includeMatches) {\n        result.indices = [[0, text.length - 1]];\n      }\n\n      return result\n    }\n\n    // Otherwise, use Bitap algorithm\n    const {\n      location,\n      distance,\n      threshold,\n      findAllMatches,\n      minMatchCharLength,\n      ignoreLocation\n    } = this.options;\n\n    let allIndices = [];\n    let totalScore = 0;\n    let hasMatches = false;\n\n    this.chunks.forEach(({ pattern, alphabet, startIndex }) => {\n      const { isMatch, score, indices } = search(text, pattern, alphabet, {\n        location: location + startIndex,\n        distance,\n        threshold,\n        findAllMatches,\n        minMatchCharLength,\n        includeMatches,\n        ignoreLocation\n      });\n\n      if (isMatch) {\n        hasMatches = true;\n      }\n\n      totalScore += score;\n\n      if (isMatch && indices) {\n        allIndices = [...allIndices, ...indices];\n      }\n    });\n\n    let result = {\n      isMatch: hasMatches,\n      score: hasMatches ? totalScore / this.chunks.length : 1\n    };\n\n    if (hasMatches && includeMatches) {\n      result.indices = allIndices;\n    }\n\n    return result\n  }\n}\n\nclass BaseMatch {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n  static isMultiMatch(pattern) {\n    return getMatch(pattern, this.multiRegex)\n  }\n  static isSingleMatch(pattern) {\n    return getMatch(pattern, this.singleRegex)\n  }\n  search(/*text*/) {}\n}\n\nfunction getMatch(pattern, exp) {\n  const matches = pattern.match(exp);\n  return matches ? matches[1] : null\n}\n\n// Token: 'file\n\nclass ExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'exact'\n  }\n  static get multiRegex() {\n    return /^=\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^=(.*)$/\n  }\n  search(text) {\n    const isMatch = text === this.pattern;\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, this.pattern.length - 1]\n    }\n  }\n}\n\n// Token: !fire\n\nclass InverseExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'inverse-exact'\n  }\n  static get multiRegex() {\n    return /^!\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^!(.*)$/\n  }\n  search(text) {\n    const index = text.indexOf(this.pattern);\n    const isMatch = index === -1;\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, text.length - 1]\n    }\n  }\n}\n\n// Token: ^file\n\nclass PrefixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'prefix-exact'\n  }\n  static get multiRegex() {\n    return /^\\^\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^\\^(.*)$/\n  }\n  search(text) {\n    const isMatch = text.startsWith(this.pattern);\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, this.pattern.length - 1]\n    }\n  }\n}\n\n// Token: !^fire\n\nclass InversePrefixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'inverse-prefix-exact'\n  }\n  static get multiRegex() {\n    return /^!\\^\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^!\\^(.*)$/\n  }\n  search(text) {\n    const isMatch = !text.startsWith(this.pattern);\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, text.length - 1]\n    }\n  }\n}\n\n// Token: .file$\n\nclass SuffixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'suffix-exact'\n  }\n  static get multiRegex() {\n    return /^\"(.*)\"\\$$/\n  }\n  static get singleRegex() {\n    return /^(.*)\\$$/\n  }\n  search(text) {\n    const isMatch = text.endsWith(this.pattern);\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [text.length - this.pattern.length, text.length - 1]\n    }\n  }\n}\n\n// Token: !.file$\n\nclass InverseSuffixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'inverse-suffix-exact'\n  }\n  static get multiRegex() {\n    return /^!\"(.*)\"\\$$/\n  }\n  static get singleRegex() {\n    return /^!(.*)\\$$/\n  }\n  search(text) {\n    const isMatch = !text.endsWith(this.pattern);\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, text.length - 1]\n    }\n  }\n}\n\nclass FuzzyMatch extends BaseMatch {\n  constructor(\n    pattern,\n    {\n      location = Config.location,\n      threshold = Config.threshold,\n      distance = Config.distance,\n      includeMatches = Config.includeMatches,\n      findAllMatches = Config.findAllMatches,\n      minMatchCharLength = Config.minMatchCharLength,\n      isCaseSensitive = Config.isCaseSensitive,\n      ignoreLocation = Config.ignoreLocation\n    } = {}\n  ) {\n    super(pattern);\n    this._bitapSearch = new BitapSearch(pattern, {\n      location,\n      threshold,\n      distance,\n      includeMatches,\n      findAllMatches,\n      minMatchCharLength,\n      isCaseSensitive,\n      ignoreLocation\n    });\n  }\n  static get type() {\n    return 'fuzzy'\n  }\n  static get multiRegex() {\n    return /^\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^(.*)$/\n  }\n  search(text) {\n    return this._bitapSearch.searchIn(text)\n  }\n}\n\n// Token: 'file\n\nclass IncludeMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'include'\n  }\n  static get multiRegex() {\n    return /^'\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^'(.*)$/\n  }\n  search(text) {\n    let location = 0;\n    let index;\n\n    const indices = [];\n    const patternLen = this.pattern.length;\n\n    // Get all exact matches\n    while ((index = text.indexOf(this.pattern, location)) > -1) {\n      location = index + patternLen;\n      indices.push([index, location - 1]);\n    }\n\n    const isMatch = !!indices.length;\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices\n    }\n  }\n}\n\n// ❗Order is important. DO NOT CHANGE.\nconst searchers = [\n  ExactMatch,\n  IncludeMatch,\n  PrefixExactMatch,\n  InversePrefixExactMatch,\n  InverseSuffixExactMatch,\n  SuffixExactMatch,\n  InverseExactMatch,\n  FuzzyMatch\n];\n\nconst searchersLen = searchers.length;\n\n// Regex to split by spaces, but keep anything in quotes together\nconst SPACE_RE = / +(?=(?:[^\\\"]*\\\"[^\\\"]*\\\")*[^\\\"]*$)/;\nconst OR_TOKEN = '|';\n\n// Return a 2D array representation of the query, for simpler parsing.\n// Example:\n// \"^core go$ | rb$ | py$ xy$\" => [[\"^core\", \"go$\"], [\"rb$\"], [\"py$\", \"xy$\"]]\nfunction parseQuery(pattern, options = {}) {\n  return pattern.split(OR_TOKEN).map((item) => {\n    let query = item\n      .trim()\n      .split(SPACE_RE)\n      .filter((item) => item && !!item.trim());\n\n    let results = [];\n    for (let i = 0, len = query.length; i < len; i += 1) {\n      const queryItem = query[i];\n\n      // 1. Handle multiple query match (i.e, once that are quoted, like `\"hello world\"`)\n      let found = false;\n      let idx = -1;\n      while (!found && ++idx < searchersLen) {\n        const searcher = searchers[idx];\n        let token = searcher.isMultiMatch(queryItem);\n        if (token) {\n          results.push(new searcher(token, options));\n          found = true;\n        }\n      }\n\n      if (found) {\n        continue\n      }\n\n      // 2. Handle single query matches (i.e, once that are *not* quoted)\n      idx = -1;\n      while (++idx < searchersLen) {\n        const searcher = searchers[idx];\n        let token = searcher.isSingleMatch(queryItem);\n        if (token) {\n          results.push(new searcher(token, options));\n          break\n        }\n      }\n    }\n\n    return results\n  })\n}\n\n// These extended matchers can return an array of matches, as opposed\n// to a singl match\nconst MultiMatchSet = new Set([FuzzyMatch.type, IncludeMatch.type]);\n\n/**\n * Command-like searching\n * ======================\n *\n * Given multiple search terms delimited by spaces.e.g. `^jscript .python$ ruby !java`,\n * search in a given text.\n *\n * Search syntax:\n *\n * | Token       | Match type                 | Description                            |\n * | ----------- | -------------------------- | -------------------------------------- |\n * | `jscript`   | fuzzy-match                | Items that fuzzy match `jscript`       |\n * | `=scheme`   | exact-match                | Items that are `scheme`                |\n * | `'python`   | include-match              | Items that include `python`            |\n * | `!ruby`     | inverse-exact-match        | Items that do not include `ruby`       |\n * | `^java`     | prefix-exact-match         | Items that start with `java`           |\n * | `!^earlang` | inverse-prefix-exact-match | Items that do not start with `earlang` |\n * | `.js$`      | suffix-exact-match         | Items that end with `.js`              |\n * | `!.go$`     | inverse-suffix-exact-match | Items that do not end with `.go`       |\n *\n * A single pipe character acts as an OR operator. For example, the following\n * query matches entries that start with `core` and end with either`go`, `rb`,\n * or`py`.\n *\n * ```\n * ^core go$ | rb$ | py$\n * ```\n */\nclass ExtendedSearch {\n  constructor(\n    pattern,\n    {\n      isCaseSensitive = Config.isCaseSensitive,\n      includeMatches = Config.includeMatches,\n      minMatchCharLength = Config.minMatchCharLength,\n      ignoreLocation = Config.ignoreLocation,\n      findAllMatches = Config.findAllMatches,\n      location = Config.location,\n      threshold = Config.threshold,\n      distance = Config.distance\n    } = {}\n  ) {\n    this.query = null;\n    this.options = {\n      isCaseSensitive,\n      includeMatches,\n      minMatchCharLength,\n      findAllMatches,\n      ignoreLocation,\n      location,\n      threshold,\n      distance\n    };\n\n    this.pattern = isCaseSensitive ? pattern : pattern.toLowerCase();\n    this.query = parseQuery(this.pattern, this.options);\n  }\n\n  static condition(_, options) {\n    return options.useExtendedSearch\n  }\n\n  searchIn(text) {\n    const query = this.query;\n\n    if (!query) {\n      return {\n        isMatch: false,\n        score: 1\n      }\n    }\n\n    const { includeMatches, isCaseSensitive } = this.options;\n\n    text = isCaseSensitive ? text : text.toLowerCase();\n\n    let numMatches = 0;\n    let allIndices = [];\n    let totalScore = 0;\n\n    // ORs\n    for (let i = 0, qLen = query.length; i < qLen; i += 1) {\n      const searchers = query[i];\n\n      // Reset indices\n      allIndices.length = 0;\n      numMatches = 0;\n\n      // ANDs\n      for (let j = 0, pLen = searchers.length; j < pLen; j += 1) {\n        const searcher = searchers[j];\n        const { isMatch, indices, score } = searcher.search(text);\n\n        if (isMatch) {\n          numMatches += 1;\n          totalScore += score;\n          if (includeMatches) {\n            const type = searcher.constructor.type;\n            if (MultiMatchSet.has(type)) {\n              allIndices = [...allIndices, ...indices];\n            } else {\n              allIndices.push(indices);\n            }\n          }\n        } else {\n          totalScore = 0;\n          numMatches = 0;\n          allIndices.length = 0;\n          break\n        }\n      }\n\n      // OR condition, so if TRUE, return\n      if (numMatches) {\n        let result = {\n          isMatch: true,\n          score: totalScore / numMatches\n        };\n\n        if (includeMatches) {\n          result.indices = allIndices;\n        }\n\n        return result\n      }\n    }\n\n    // Nothing was matched\n    return {\n      isMatch: false,\n      score: 1\n    }\n  }\n}\n\nconst registeredSearchers = [];\n\nfunction register(...args) {\n  registeredSearchers.push(...args);\n}\n\nfunction createSearcher(pattern, options) {\n  for (let i = 0, len = registeredSearchers.length; i < len; i += 1) {\n    let searcherClass = registeredSearchers[i];\n    if (searcherClass.condition(pattern, options)) {\n      return new searcherClass(pattern, options)\n    }\n  }\n\n  return new BitapSearch(pattern, options)\n}\n\nconst LogicalOperator = {\n  AND: '$and',\n  OR: '$or'\n};\n\nconst KeyType = {\n  PATH: '$path',\n  PATTERN: '$val'\n};\n\nconst isExpression = (query) =>\n  !!(query[LogicalOperator.AND] || query[LogicalOperator.OR]);\n\nconst isPath = (query) => !!query[KeyType.PATH];\n\nconst isLeaf = (query) =>\n  !isArray(query) && isObject(query) && !isExpression(query);\n\nconst convertToExplicit = (query) => ({\n  [LogicalOperator.AND]: Object.keys(query).map((key) => ({\n    [key]: query[key]\n  }))\n});\n\n// When `auto` is `true`, the parse function will infer and initialize and add\n// the appropriate `Searcher` instance\nfunction parse(query, options, { auto = true } = {}) {\n  const next = (query) => {\n    let keys = Object.keys(query);\n\n    const isQueryPath = isPath(query);\n\n    if (!isQueryPath && keys.length > 1 && !isExpression(query)) {\n      return next(convertToExplicit(query))\n    }\n\n    if (isLeaf(query)) {\n      const key = isQueryPath ? query[KeyType.PATH] : keys[0];\n\n      const pattern = isQueryPath ? query[KeyType.PATTERN] : query[key];\n\n      if (!isString(pattern)) {\n        throw new Error(LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY(key))\n      }\n\n      const obj = {\n        keyId: createKeyId(key),\n        pattern\n      };\n\n      if (auto) {\n        obj.searcher = createSearcher(pattern, options);\n      }\n\n      return obj\n    }\n\n    let node = {\n      children: [],\n      operator: keys[0]\n    };\n\n    keys.forEach((key) => {\n      const value = query[key];\n\n      if (isArray(value)) {\n        value.forEach((item) => {\n          node.children.push(next(item));\n        });\n      }\n    });\n\n    return node\n  };\n\n  if (!isExpression(query)) {\n    query = convertToExplicit(query);\n  }\n\n  return next(query)\n}\n\n// Practical scoring function\nfunction computeScore(\n  results,\n  { ignoreFieldNorm = Config.ignoreFieldNorm }\n) {\n  results.forEach((result) => {\n    let totalScore = 1;\n\n    result.matches.forEach(({ key, norm, score }) => {\n      const weight = key ? key.weight : null;\n\n      totalScore *= Math.pow(\n        score === 0 && weight ? Number.EPSILON : score,\n        (weight || 1) * (ignoreFieldNorm ? 1 : norm)\n      );\n    });\n\n    result.score = totalScore;\n  });\n}\n\nfunction transformMatches(result, data) {\n  const matches = result.matches;\n  data.matches = [];\n\n  if (!isDefined(matches)) {\n    return\n  }\n\n  matches.forEach((match) => {\n    if (!isDefined(match.indices) || !match.indices.length) {\n      return\n    }\n\n    const { indices, value } = match;\n\n    let obj = {\n      indices,\n      value\n    };\n\n    if (match.key) {\n      obj.key = match.key.src;\n    }\n\n    if (match.idx > -1) {\n      obj.refIndex = match.idx;\n    }\n\n    data.matches.push(obj);\n  });\n}\n\nfunction transformScore(result, data) {\n  data.score = result.score;\n}\n\nfunction format(\n  results,\n  docs,\n  {\n    includeMatches = Config.includeMatches,\n    includeScore = Config.includeScore\n  } = {}\n) {\n  const transformers = [];\n\n  if (includeMatches) transformers.push(transformMatches);\n  if (includeScore) transformers.push(transformScore);\n\n  return results.map((result) => {\n    const { idx } = result;\n\n    const data = {\n      item: docs[idx],\n      refIndex: idx\n    };\n\n    if (transformers.length) {\n      transformers.forEach((transformer) => {\n        transformer(result, data);\n      });\n    }\n\n    return data\n  })\n}\n\nclass Fuse {\n  constructor(docs, options = {}, index) {\n    this.options = { ...Config, ...options };\n\n    if (\n      this.options.useExtendedSearch &&\n      !true\n    ) {\n      throw new Error(EXTENDED_SEARCH_UNAVAILABLE)\n    }\n\n    this._keyStore = new KeyStore(this.options.keys);\n\n    this.setCollection(docs, index);\n  }\n\n  setCollection(docs, index) {\n    this._docs = docs;\n\n    if (index && !(index instanceof FuseIndex)) {\n      throw new Error(INCORRECT_INDEX_TYPE)\n    }\n\n    this._myIndex =\n      index ||\n      createIndex(this.options.keys, this._docs, {\n        getFn: this.options.getFn,\n        fieldNormWeight: this.options.fieldNormWeight\n      });\n  }\n\n  add(doc) {\n    if (!isDefined(doc)) {\n      return\n    }\n\n    this._docs.push(doc);\n    this._myIndex.add(doc);\n  }\n\n  remove(predicate = (/* doc, idx */) => false) {\n    const results = [];\n\n    for (let i = 0, len = this._docs.length; i < len; i += 1) {\n      const doc = this._docs[i];\n      if (predicate(doc, i)) {\n        this.removeAt(i);\n        i -= 1;\n        len -= 1;\n\n        results.push(doc);\n      }\n    }\n\n    return results\n  }\n\n  removeAt(idx) {\n    this._docs.splice(idx, 1);\n    this._myIndex.removeAt(idx);\n  }\n\n  getIndex() {\n    return this._myIndex\n  }\n\n  search(query, { limit = -1 } = {}) {\n    const {\n      includeMatches,\n      includeScore,\n      shouldSort,\n      sortFn,\n      ignoreFieldNorm\n    } = this.options;\n\n    let results = isString(query)\n      ? isString(this._docs[0])\n        ? this._searchStringList(query)\n        : this._searchObjectList(query)\n      : this._searchLogical(query);\n\n    computeScore(results, { ignoreFieldNorm });\n\n    if (shouldSort) {\n      results.sort(sortFn);\n    }\n\n    if (isNumber(limit) && limit > -1) {\n      results = results.slice(0, limit);\n    }\n\n    return format(results, this._docs, {\n      includeMatches,\n      includeScore\n    })\n  }\n\n  _searchStringList(query) {\n    const searcher = createSearcher(query, this.options);\n    const { records } = this._myIndex;\n    const results = [];\n\n    // Iterate over every string in the index\n    records.forEach(({ v: text, i: idx, n: norm }) => {\n      if (!isDefined(text)) {\n        return\n      }\n\n      const { isMatch, score, indices } = searcher.searchIn(text);\n\n      if (isMatch) {\n        results.push({\n          item: text,\n          idx,\n          matches: [{ score, value: text, norm, indices }]\n        });\n      }\n    });\n\n    return results\n  }\n\n  _searchLogical(query) {\n\n    const expression = parse(query, this.options);\n\n    const evaluate = (node, item, idx) => {\n      if (!node.children) {\n        const { keyId, searcher } = node;\n\n        const matches = this._findMatches({\n          key: this._keyStore.get(keyId),\n          value: this._myIndex.getValueForItemAtKeyId(item, keyId),\n          searcher\n        });\n\n        if (matches && matches.length) {\n          return [\n            {\n              idx,\n              item,\n              matches\n            }\n          ]\n        }\n\n        return []\n      }\n\n      const res = [];\n      for (let i = 0, len = node.children.length; i < len; i += 1) {\n        const child = node.children[i];\n        const result = evaluate(child, item, idx);\n        if (result.length) {\n          res.push(...result);\n        } else if (node.operator === LogicalOperator.AND) {\n          return []\n        }\n      }\n      return res\n    };\n\n    const records = this._myIndex.records;\n    const resultMap = {};\n    const results = [];\n\n    records.forEach(({ $: item, i: idx }) => {\n      if (isDefined(item)) {\n        let expResults = evaluate(expression, item, idx);\n\n        if (expResults.length) {\n          // Dedupe when adding\n          if (!resultMap[idx]) {\n            resultMap[idx] = { idx, item, matches: [] };\n            results.push(resultMap[idx]);\n          }\n          expResults.forEach(({ matches }) => {\n            resultMap[idx].matches.push(...matches);\n          });\n        }\n      }\n    });\n\n    return results\n  }\n\n  _searchObjectList(query) {\n    const searcher = createSearcher(query, this.options);\n    const { keys, records } = this._myIndex;\n    const results = [];\n\n    // List is Array<Object>\n    records.forEach(({ $: item, i: idx }) => {\n      if (!isDefined(item)) {\n        return\n      }\n\n      let matches = [];\n\n      // Iterate over every key (i.e, path), and fetch the value at that key\n      keys.forEach((key, keyIndex) => {\n        matches.push(\n          ...this._findMatches({\n            key,\n            value: item[keyIndex],\n            searcher\n          })\n        );\n      });\n\n      if (matches.length) {\n        results.push({\n          idx,\n          item,\n          matches\n        });\n      }\n    });\n\n    return results\n  }\n  _findMatches({ key, value, searcher }) {\n    if (!isDefined(value)) {\n      return []\n    }\n\n    let matches = [];\n\n    if (isArray(value)) {\n      value.forEach(({ v: text, i: idx, n: norm }) => {\n        if (!isDefined(text)) {\n          return\n        }\n\n        const { isMatch, score, indices } = searcher.searchIn(text);\n\n        if (isMatch) {\n          matches.push({\n            score,\n            key,\n            value: text,\n            idx,\n            norm,\n            indices\n          });\n        }\n      });\n    } else {\n      const { v: text, n: norm } = value;\n\n      const { isMatch, score, indices } = searcher.searchIn(text);\n\n      if (isMatch) {\n        matches.push({ score, key, value: text, norm, indices });\n      }\n    }\n\n    return matches\n  }\n}\n\nFuse.version = '6.6.2';\nFuse.createIndex = createIndex;\nFuse.parseIndex = parseIndex;\nFuse.config = Config;\n\n{\n  Fuse.parseQuery = parse;\n}\n\n{\n  register(ExtendedSearch);\n}\n\nexport { Fuse as default };\n"], "mappings": ";;;AASA,SAAS,QAAQ,OAAO;AACtB,SAAO,CAAC,MAAM,UACV,OAAO,KAAK,MAAM,mBAClB,MAAM,QAAQ,KAAK;AACzB;AAGA,IAAM,WAAW,IAAI;AACrB,SAAS,aAAa,OAAO;AAE3B,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,SAAS,QAAQ;AACrB,SAAO,UAAU,OAAO,IAAI,SAAS,CAAC,WAAW,OAAO;AAC1D;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,SAAS,OAAO,KAAK,aAAa,KAAK;AAChD;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AAGA,SAAS,UAAU,OAAO;AACxB,SACE,UAAU,QACV,UAAU,SACT,aAAa,KAAK,KAAK,OAAO,KAAK,KAAK;AAE7C;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AAGA,SAAS,aAAa,OAAO;AAC3B,SAAO,SAAS,KAAK,KAAK,UAAU;AACtC;AAEA,SAAS,UAAU,OAAO;AACxB,SAAO,UAAU,UAAa,UAAU;AAC1C;AAEA,SAAS,QAAQ,OAAO;AACtB,SAAO,CAAC,MAAM,KAAK,EAAE;AACvB;AAIA,SAAS,OAAO,OAAO;AACrB,SAAO,SAAS,OACZ,UAAU,SACR,uBACA,kBACF,OAAO,UAAU,SAAS,KAAK,KAAK;AAC1C;AAIA,IAAM,uBAAuB;AAE7B,IAAM,uCAAuC,CAAC,QAC5C,yBAAyB,GAAG;AAE9B,IAAM,2BAA2B,CAAC,QAChC,iCAAiC,GAAG;AAEtC,IAAM,uBAAuB,CAAC,SAAS,WAAW,IAAI;AAEtD,IAAM,2BAA2B,CAAC,QAChC,6BAA6B,GAAG;AAElC,IAAM,SAAS,OAAO,UAAU;AAEhC,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,MAAM;AAChB,SAAK,QAAQ,CAAC;AACd,SAAK,UAAU,CAAC;AAEhB,QAAI,cAAc;AAElB,SAAK,QAAQ,CAAC,QAAQ;AACpB,UAAI,MAAM,UAAU,GAAG;AAEvB,qBAAe,IAAI;AAEnB,WAAK,MAAM,KAAK,GAAG;AACnB,WAAK,QAAQ,IAAI,EAAE,IAAI;AAEvB,qBAAe,IAAI;AAAA,IACrB,CAAC;AAGD,SAAK,MAAM,QAAQ,CAAC,QAAQ;AAC1B,UAAI,UAAU;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,KAAK;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AAAA,EACA,SAAS;AACP,WAAO,KAAK,UAAU,KAAK,KAAK;AAAA,EAClC;AACF;AAEA,SAAS,UAAU,KAAK;AACtB,MAAI,OAAO;AACX,MAAI,KAAK;AACT,MAAI,MAAM;AACV,MAAI,SAAS;AACb,MAAI,QAAQ;AAEZ,MAAI,SAAS,GAAG,KAAK,QAAQ,GAAG,GAAG;AACjC,UAAM;AACN,WAAO,cAAc,GAAG;AACxB,SAAK,YAAY,GAAG;AAAA,EACtB,OAAO;AACL,QAAI,CAAC,OAAO,KAAK,KAAK,MAAM,GAAG;AAC7B,YAAM,IAAI,MAAM,qBAAqB,MAAM,CAAC;AAAA,IAC9C;AAEA,UAAM,OAAO,IAAI;AACjB,UAAM;AAEN,QAAI,OAAO,KAAK,KAAK,QAAQ,GAAG;AAC9B,eAAS,IAAI;AAEb,UAAI,UAAU,GAAG;AACf,cAAM,IAAI,MAAM,yBAAyB,IAAI,CAAC;AAAA,MAChD;AAAA,IACF;AAEA,WAAO,cAAc,IAAI;AACzB,SAAK,YAAY,IAAI;AACrB,YAAQ,IAAI;AAAA,EACd;AAEA,SAAO,EAAE,MAAM,IAAI,QAAQ,KAAK,MAAM;AACxC;AAEA,SAAS,cAAc,KAAK;AAC1B,SAAO,QAAQ,GAAG,IAAI,MAAM,IAAI,MAAM,GAAG;AAC3C;AAEA,SAAS,YAAY,KAAK;AACxB,SAAO,QAAQ,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI;AACxC;AAEA,SAAS,IAAI,KAAK,MAAM;AACtB,MAAI,OAAO,CAAC;AACZ,MAAI,MAAM;AAEV,QAAM,UAAU,CAACA,MAAKC,OAAM,UAAU;AACpC,QAAI,CAAC,UAAUD,IAAG,GAAG;AACnB;AAAA,IACF;AACA,QAAI,CAACC,MAAK,KAAK,GAAG;AAEhB,WAAK,KAAKD,IAAG;AAAA,IACf,OAAO;AACL,UAAI,MAAMC,MAAK,KAAK;AAEpB,YAAM,QAAQD,KAAI,GAAG;AAErB,UAAI,CAAC,UAAU,KAAK,GAAG;AACrB;AAAA,MACF;AAIA,UACE,UAAUC,MAAK,SAAS,MACvB,SAAS,KAAK,KAAK,SAAS,KAAK,KAAK,UAAU,KAAK,IACtD;AACA,aAAK,KAAK,SAAS,KAAK,CAAC;AAAA,MAC3B,WAAW,QAAQ,KAAK,GAAG;AACzB,cAAM;AAEN,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK,GAAG;AACnD,kBAAQ,MAAM,CAAC,GAAGA,OAAM,QAAQ,CAAC;AAAA,QACnC;AAAA,MACF,WAAWA,MAAK,QAAQ;AAEtB,gBAAQ,OAAOA,OAAM,QAAQ,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAGA,UAAQ,KAAK,SAAS,IAAI,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC;AAEvD,SAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEA,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA,EAInB,gBAAgB;AAAA;AAAA;AAAA,EAGhB,gBAAgB;AAAA;AAAA,EAEhB,oBAAoB;AACtB;AAEA,IAAM,eAAe;AAAA;AAAA;AAAA,EAGnB,iBAAiB;AAAA;AAAA,EAEjB,cAAc;AAAA;AAAA,EAEd,MAAM,CAAC;AAAA;AAAA,EAEP,YAAY;AAAA;AAAA,EAEZ,QAAQ,CAAC,GAAG,MACV,EAAE,UAAU,EAAE,QAAS,EAAE,MAAM,EAAE,MAAM,KAAK,IAAK,EAAE,QAAQ,EAAE,QAAQ,KAAK;AAC9E;AAEA,IAAM,eAAe;AAAA;AAAA,EAEnB,UAAU;AAAA;AAAA;AAAA,EAGV,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,UAAU;AACZ;AAEA,IAAM,kBAAkB;AAAA;AAAA,EAEtB,mBAAmB;AAAA;AAAA;AAAA,EAGnB,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,gBAAgB;AAAA;AAAA;AAAA;AAAA,EAIhB,iBAAiB;AAAA;AAAA,EAEjB,iBAAiB;AACnB;AAEA,IAAI,SAAS;AAAA,EACX,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AAEA,IAAM,QAAQ;AAId,SAAS,KAAK,SAAS,GAAG,WAAW,GAAG;AACtC,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,IAAI,KAAK,IAAI,IAAI,QAAQ;AAE/B,SAAO;AAAA,IACL,IAAI,OAAO;AACT,YAAM,YAAY,MAAM,MAAM,KAAK,EAAE;AAErC,UAAI,MAAM,IAAI,SAAS,GAAG;AACxB,eAAO,MAAM,IAAI,SAAS;AAAA,MAC5B;AAGA,YAAMC,QAAO,IAAI,KAAK,IAAI,WAAW,MAAM,MAAM;AAGjD,YAAM,IAAI,WAAW,KAAK,MAAMA,QAAO,CAAC,IAAI,CAAC;AAE7C,YAAM,IAAI,WAAW,CAAC;AAEtB,aAAO;AAAA,IACT;AAAA,IACA,QAAQ;AACN,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AACF;AAEA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY;AAAA,IACV,QAAQ,OAAO;AAAA,IACf,kBAAkB,OAAO;AAAA,EAC3B,IAAI,CAAC,GAAG;AACN,SAAK,OAAO,KAAK,iBAAiB,CAAC;AACnC,SAAK,QAAQ;AACb,SAAK,YAAY;AAEjB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,WAAW,OAAO,CAAC,GAAG;AACpB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,gBAAgB,UAAU,CAAC,GAAG;AAC5B,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,QAAQ,OAAO,CAAC,GAAG;AACjB,SAAK,OAAO;AACZ,SAAK,WAAW,CAAC;AACjB,SAAK,QAAQ,CAAC,KAAK,QAAQ;AACzB,WAAK,SAAS,IAAI,EAAE,IAAI;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AACP,QAAI,KAAK,aAAa,CAAC,KAAK,KAAK,QAAQ;AACvC;AAAA,IACF;AAEA,SAAK,YAAY;AAGjB,QAAI,SAAS,KAAK,KAAK,CAAC,CAAC,GAAG;AAC1B,WAAK,KAAK,QAAQ,CAAC,KAAK,aAAa;AACnC,aAAK,WAAW,KAAK,QAAQ;AAAA,MAC/B,CAAC;AAAA,IACH,OAAO;AAEL,WAAK,KAAK,QAAQ,CAAC,KAAK,aAAa;AACnC,aAAK,WAAW,KAAK,QAAQ;AAAA,MAC/B,CAAC;AAAA,IACH;AAEA,SAAK,KAAK,MAAM;AAAA,EAClB;AAAA;AAAA,EAEA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,KAAK;AAEtB,QAAI,SAAS,GAAG,GAAG;AACjB,WAAK,WAAW,KAAK,GAAG;AAAA,IAC1B,OAAO;AACL,WAAK,WAAW,KAAK,GAAG;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,KAAK;AACZ,SAAK,QAAQ,OAAO,KAAK,CAAC;AAG1B,aAAS,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG;AACpD,WAAK,QAAQ,CAAC,EAAE,KAAK;AAAA,IACvB;AAAA,EACF;AAAA,EACA,uBAAuB,MAAM,OAAO;AAClC,WAAO,KAAK,KAAK,SAAS,KAAK,CAAC;AAAA,EAClC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,WAAW,KAAK,UAAU;AACxB,QAAI,CAAC,UAAU,GAAG,KAAK,QAAQ,GAAG,GAAG;AACnC;AAAA,IACF;AAEA,QAAI,SAAS;AAAA,MACX,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,IACtB;AAEA,SAAK,QAAQ,KAAK,MAAM;AAAA,EAC1B;AAAA,EACA,WAAW,KAAK,UAAU;AACxB,QAAI,SAAS,EAAE,GAAG,UAAU,GAAG,CAAC,EAAE;AAGlC,SAAK,KAAK,QAAQ,CAAC,KAAK,aAAa;AACnC,UAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI;AAEjE,UAAI,CAAC,UAAU,KAAK,GAAG;AACrB;AAAA,MACF;AAEA,UAAI,QAAQ,KAAK,GAAG;AAClB,YAAI,aAAa,CAAC;AAClB,cAAM,QAAQ,CAAC,EAAE,gBAAgB,IAAI,MAAM,CAAC;AAE5C,eAAO,MAAM,QAAQ;AACnB,gBAAM,EAAE,gBAAgB,OAAAC,OAAM,IAAI,MAAM,IAAI;AAE5C,cAAI,CAAC,UAAUA,MAAK,GAAG;AACrB;AAAA,UACF;AAEA,cAAI,SAASA,MAAK,KAAK,CAAC,QAAQA,MAAK,GAAG;AACtC,gBAAI,YAAY;AAAA,cACd,GAAGA;AAAA,cACH,GAAG;AAAA,cACH,GAAG,KAAK,KAAK,IAAIA,MAAK;AAAA,YACxB;AAEA,uBAAW,KAAK,SAAS;AAAA,UAC3B,WAAW,QAAQA,MAAK,GAAG;AACzB,YAAAA,OAAM,QAAQ,CAAC,MAAM,MAAM;AACzB,oBAAM,KAAK;AAAA,gBACT,gBAAgB;AAAA,gBAChB,OAAO;AAAA,cACT,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAO;AAAA,QACT;AACA,eAAO,EAAE,QAAQ,IAAI;AAAA,MACvB,WAAW,SAAS,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;AAC7C,YAAI,YAAY;AAAA,UACd,GAAG;AAAA,UACH,GAAG,KAAK,KAAK,IAAI,KAAK;AAAA,QACxB;AAEA,eAAO,EAAE,QAAQ,IAAI;AAAA,MACvB;AAAA,IACF,CAAC;AAED,SAAK,QAAQ,KAAK,MAAM;AAAA,EAC1B;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,IAChB;AAAA,EACF;AACF;AAEA,SAAS,YACP,MACA,MACA,EAAE,QAAQ,OAAO,OAAO,kBAAkB,OAAO,gBAAgB,IAAI,CAAC,GACtE;AACA,QAAM,UAAU,IAAI,UAAU,EAAE,OAAO,gBAAgB,CAAC;AACxD,UAAQ,QAAQ,KAAK,IAAI,SAAS,CAAC;AACnC,UAAQ,WAAW,IAAI;AACvB,UAAQ,OAAO;AACf,SAAO;AACT;AAEA,SAAS,WACP,MACA,EAAE,QAAQ,OAAO,OAAO,kBAAkB,OAAO,gBAAgB,IAAI,CAAC,GACtE;AACA,QAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,QAAM,UAAU,IAAI,UAAU,EAAE,OAAO,gBAAgB,CAAC;AACxD,UAAQ,QAAQ,IAAI;AACpB,UAAQ,gBAAgB,OAAO;AAC/B,SAAO;AACT;AAEA,SAAS,eACP,SACA;AAAA,EACE,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,WAAW,OAAO;AAAA,EAClB,iBAAiB,OAAO;AAC1B,IAAI,CAAC,GACL;AACA,QAAM,WAAW,SAAS,QAAQ;AAElC,MAAI,gBAAgB;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,KAAK,IAAI,mBAAmB,eAAe;AAE7D,MAAI,CAAC,UAAU;AAEb,WAAO,YAAY,IAAM;AAAA,EAC3B;AAEA,SAAO,WAAW,YAAY;AAChC;AAEA,SAAS,qBACP,YAAY,CAAC,GACb,qBAAqB,OAAO,oBAC5B;AACA,MAAI,UAAU,CAAC;AACf,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,MAAI,IAAI;AAER,WAAS,MAAM,UAAU,QAAQ,IAAI,KAAK,KAAK,GAAG;AAChD,QAAI,QAAQ,UAAU,CAAC;AACvB,QAAI,SAAS,UAAU,IAAI;AACzB,cAAQ;AAAA,IACV,WAAW,CAAC,SAAS,UAAU,IAAI;AACjC,YAAM,IAAI;AACV,UAAI,MAAM,QAAQ,KAAK,oBAAoB;AACzC,gBAAQ,KAAK,CAAC,OAAO,GAAG,CAAC;AAAA,MAC3B;AACA,cAAQ;AAAA,IACV;AAAA,EACF;AAGA,MAAI,UAAU,IAAI,CAAC,KAAK,IAAI,SAAS,oBAAoB;AACvD,YAAQ,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;AAAA,EAC7B;AAEA,SAAO;AACT;AAGA,IAAM,WAAW;AAEjB,SAAS,OACP,MACA,SACA,iBACA;AAAA,EACE,WAAW,OAAO;AAAA,EAClB,WAAW,OAAO;AAAA,EAClB,YAAY,OAAO;AAAA,EACnB,iBAAiB,OAAO;AAAA,EACxB,qBAAqB,OAAO;AAAA,EAC5B,iBAAiB,OAAO;AAAA,EACxB,iBAAiB,OAAO;AAC1B,IAAI,CAAC,GACL;AACA,MAAI,QAAQ,SAAS,UAAU;AAC7B,UAAM,IAAI,MAAM,yBAAyB,QAAQ,CAAC;AAAA,EACpD;AAEA,QAAM,aAAa,QAAQ;AAE3B,QAAM,UAAU,KAAK;AAErB,QAAM,mBAAmB,KAAK,IAAI,GAAG,KAAK,IAAI,UAAU,OAAO,CAAC;AAEhE,MAAI,mBAAmB;AAEvB,MAAI,eAAe;AAInB,QAAM,iBAAiB,qBAAqB,KAAK;AAEjD,QAAM,YAAY,iBAAiB,MAAM,OAAO,IAAI,CAAC;AAErD,MAAI;AAGJ,UAAQ,QAAQ,KAAK,QAAQ,SAAS,YAAY,KAAK,IAAI;AACzD,QAAI,QAAQ,eAAe,SAAS;AAAA,MAClC,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,uBAAmB,KAAK,IAAI,OAAO,gBAAgB;AACnD,mBAAe,QAAQ;AAEvB,QAAI,gBAAgB;AAClB,UAAI,IAAI;AACR,aAAO,IAAI,YAAY;AACrB,kBAAU,QAAQ,CAAC,IAAI;AACvB,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAGA,iBAAe;AAEf,MAAI,aAAa,CAAC;AAClB,MAAI,aAAa;AACjB,MAAI,SAAS,aAAa;AAE1B,QAAM,OAAO,KAAM,aAAa;AAEhC,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK,GAAG;AAItC,QAAI,SAAS;AACb,QAAI,SAAS;AAEb,WAAO,SAAS,QAAQ;AACtB,YAAMC,SAAQ,eAAe,SAAS;AAAA,QACpC,QAAQ;AAAA,QACR,iBAAiB,mBAAmB;AAAA,QACpC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,UAAIA,UAAS,kBAAkB;AAC7B,iBAAS;AAAA,MACX,OAAO;AACL,iBAAS;AAAA,MACX;AAEA,eAAS,KAAK,OAAO,SAAS,UAAU,IAAI,MAAM;AAAA,IACpD;AAGA,aAAS;AAET,QAAI,QAAQ,KAAK,IAAI,GAAG,mBAAmB,SAAS,CAAC;AACrD,QAAI,SAAS,iBACT,UACA,KAAK,IAAI,mBAAmB,QAAQ,OAAO,IAAI;AAGnD,QAAI,SAAS,MAAM,SAAS,CAAC;AAE7B,WAAO,SAAS,CAAC,KAAK,KAAK,KAAK;AAEhC,aAAS,IAAI,QAAQ,KAAK,OAAO,KAAK,GAAG;AACvC,UAAI,kBAAkB,IAAI;AAC1B,UAAI,YAAY,gBAAgB,KAAK,OAAO,eAAe,CAAC;AAE5D,UAAI,gBAAgB;AAElB,kBAAU,eAAe,IAAI,CAAC,CAAC,CAAC;AAAA,MAClC;AAGA,aAAO,CAAC,KAAM,OAAO,IAAI,CAAC,KAAK,IAAK,KAAK;AAGzC,UAAI,GAAG;AACL,eAAO,CAAC,MACJ,WAAW,IAAI,CAAC,IAAI,WAAW,CAAC,MAAM,IAAK,IAAI,WAAW,IAAI,CAAC;AAAA,MACrE;AAEA,UAAI,OAAO,CAAC,IAAI,MAAM;AACpB,qBAAa,eAAe,SAAS;AAAA,UACnC,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAID,YAAI,cAAc,kBAAkB;AAElC,6BAAmB;AACnB,yBAAe;AAGf,cAAI,gBAAgB,kBAAkB;AACpC;AAAA,UACF;AAGA,kBAAQ,KAAK,IAAI,GAAG,IAAI,mBAAmB,YAAY;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AAGA,UAAM,QAAQ,eAAe,SAAS;AAAA,MACpC,QAAQ,IAAI;AAAA,MACZ,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,QAAI,QAAQ,kBAAkB;AAC5B;AAAA,IACF;AAEA,iBAAa;AAAA,EACf;AAEA,QAAM,SAAS;AAAA,IACb,SAAS,gBAAgB;AAAA;AAAA,IAEzB,OAAO,KAAK,IAAI,MAAO,UAAU;AAAA,EACnC;AAEA,MAAI,gBAAgB;AAClB,UAAM,UAAU,qBAAqB,WAAW,kBAAkB;AAClE,QAAI,CAAC,QAAQ,QAAQ;AACnB,aAAO,UAAU;AAAA,IACnB,WAAW,gBAAgB;AACzB,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS;AACtC,MAAI,OAAO,CAAC;AAEZ,WAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK,GAAG;AACrD,UAAM,OAAO,QAAQ,OAAO,CAAC;AAC7B,SAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAM,KAAM,MAAM,IAAI;AAAA,EACpD;AAEA,SAAO;AACT;AAEA,IAAM,cAAN,MAAkB;AAAA,EAChB,YACE,SACA;AAAA,IACE,WAAW,OAAO;AAAA,IAClB,YAAY,OAAO;AAAA,IACnB,WAAW,OAAO;AAAA,IAClB,iBAAiB,OAAO;AAAA,IACxB,iBAAiB,OAAO;AAAA,IACxB,qBAAqB,OAAO;AAAA,IAC5B,kBAAkB,OAAO;AAAA,IACzB,iBAAiB,OAAO;AAAA,EAC1B,IAAI,CAAC,GACL;AACA,SAAK,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,SAAK,UAAU,kBAAkB,UAAU,QAAQ,YAAY;AAE/D,SAAK,SAAS,CAAC;AAEf,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACxB;AAAA,IACF;AAEA,UAAM,WAAW,CAACC,UAAS,eAAe;AACxC,WAAK,OAAO,KAAK;AAAA,QACf,SAAAA;AAAA,QACA,UAAU,sBAAsBA,QAAO;AAAA,QACvC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,MAAM,KAAK,QAAQ;AAEzB,QAAI,MAAM,UAAU;AAClB,UAAI,IAAI;AACR,YAAM,YAAY,MAAM;AACxB,YAAM,MAAM,MAAM;AAElB,aAAO,IAAI,KAAK;AACd,iBAAS,KAAK,QAAQ,OAAO,GAAG,QAAQ,GAAG,CAAC;AAC5C,aAAK;AAAA,MACP;AAEA,UAAI,WAAW;AACb,cAAM,aAAa,MAAM;AACzB,iBAAS,KAAK,QAAQ,OAAO,UAAU,GAAG,UAAU;AAAA,MACtD;AAAA,IACF,OAAO;AACL,eAAS,KAAK,SAAS,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA,EAEA,SAAS,MAAM;AACb,UAAM,EAAE,iBAAiB,eAAe,IAAI,KAAK;AAEjD,QAAI,CAAC,iBAAiB;AACpB,aAAO,KAAK,YAAY;AAAA,IAC1B;AAGA,QAAI,KAAK,YAAY,MAAM;AACzB,UAAIC,UAAS;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAEA,UAAI,gBAAgB;AAClB,QAAAA,QAAO,UAAU,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC;AAAA,MACxC;AAEA,aAAOA;AAAA,IACT;AAGA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AAET,QAAI,aAAa,CAAC;AAClB,QAAI,aAAa;AACjB,QAAI,aAAa;AAEjB,SAAK,OAAO,QAAQ,CAAC,EAAE,SAAS,UAAU,WAAW,MAAM;AACzD,YAAM,EAAE,SAAS,OAAO,QAAQ,IAAI,OAAO,MAAM,SAAS,UAAU;AAAA,QAClE,UAAU,WAAW;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,UAAI,SAAS;AACX,qBAAa;AAAA,MACf;AAEA,oBAAc;AAEd,UAAI,WAAW,SAAS;AACtB,qBAAa,CAAC,GAAG,YAAY,GAAG,OAAO;AAAA,MACzC;AAAA,IACF,CAAC;AAED,QAAI,SAAS;AAAA,MACX,SAAS;AAAA,MACT,OAAO,aAAa,aAAa,KAAK,OAAO,SAAS;AAAA,IACxD;AAEA,QAAI,cAAc,gBAAgB;AAChC,aAAO,UAAU;AAAA,IACnB;AAEA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO,aAAa,SAAS;AAC3B,WAAO,SAAS,SAAS,KAAK,UAAU;AAAA,EAC1C;AAAA,EACA,OAAO,cAAc,SAAS;AAC5B,WAAO,SAAS,SAAS,KAAK,WAAW;AAAA,EAC3C;AAAA,EACA,SAAiB;AAAA,EAAC;AACpB;AAEA,SAAS,SAAS,SAAS,KAAK;AAC9B,QAAM,UAAU,QAAQ,MAAM,GAAG;AACjC,SAAO,UAAU,QAAQ,CAAC,IAAI;AAChC;AAIA,IAAM,aAAN,cAAyB,UAAU;AAAA,EACjC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EACA,WAAW,OAAO;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,aAAa;AACtB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,UAAM,UAAU,SAAS,KAAK;AAE9B,WAAO;AAAA,MACL;AAAA,MACA,OAAO,UAAU,IAAI;AAAA,MACrB,SAAS,CAAC,GAAG,KAAK,QAAQ,SAAS,CAAC;AAAA,IACtC;AAAA,EACF;AACF;AAIA,IAAM,oBAAN,cAAgC,UAAU;AAAA,EACxC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EACA,WAAW,OAAO;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,aAAa;AACtB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,UAAM,QAAQ,KAAK,QAAQ,KAAK,OAAO;AACvC,UAAM,UAAU,UAAU;AAE1B,WAAO;AAAA,MACL;AAAA,MACA,OAAO,UAAU,IAAI;AAAA,MACrB,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC;AAAA,IAC9B;AAAA,EACF;AACF;AAIA,IAAM,mBAAN,cAA+B,UAAU;AAAA,EACvC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EACA,WAAW,OAAO;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,aAAa;AACtB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,UAAM,UAAU,KAAK,WAAW,KAAK,OAAO;AAE5C,WAAO;AAAA,MACL;AAAA,MACA,OAAO,UAAU,IAAI;AAAA,MACrB,SAAS,CAAC,GAAG,KAAK,QAAQ,SAAS,CAAC;AAAA,IACtC;AAAA,EACF;AACF;AAIA,IAAM,0BAAN,cAAsC,UAAU;AAAA,EAC9C,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EACA,WAAW,OAAO;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,aAAa;AACtB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,UAAM,UAAU,CAAC,KAAK,WAAW,KAAK,OAAO;AAE7C,WAAO;AAAA,MACL;AAAA,MACA,OAAO,UAAU,IAAI;AAAA,MACrB,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC;AAAA,IAC9B;AAAA,EACF;AACF;AAIA,IAAM,mBAAN,cAA+B,UAAU;AAAA,EACvC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EACA,WAAW,OAAO;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,aAAa;AACtB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,UAAM,UAAU,KAAK,SAAS,KAAK,OAAO;AAE1C,WAAO;AAAA,MACL;AAAA,MACA,OAAO,UAAU,IAAI;AAAA,MACrB,SAAS,CAAC,KAAK,SAAS,KAAK,QAAQ,QAAQ,KAAK,SAAS,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;AAIA,IAAM,0BAAN,cAAsC,UAAU;AAAA,EAC9C,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EACA,WAAW,OAAO;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,aAAa;AACtB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,UAAM,UAAU,CAAC,KAAK,SAAS,KAAK,OAAO;AAC3C,WAAO;AAAA,MACL;AAAA,MACA,OAAO,UAAU,IAAI;AAAA,MACrB,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC;AAAA,IAC9B;AAAA,EACF;AACF;AAEA,IAAM,aAAN,cAAyB,UAAU;AAAA,EACjC,YACE,SACA;AAAA,IACE,WAAW,OAAO;AAAA,IAClB,YAAY,OAAO;AAAA,IACnB,WAAW,OAAO;AAAA,IAClB,iBAAiB,OAAO;AAAA,IACxB,iBAAiB,OAAO;AAAA,IACxB,qBAAqB,OAAO;AAAA,IAC5B,kBAAkB,OAAO;AAAA,IACzB,iBAAiB,OAAO;AAAA,EAC1B,IAAI,CAAC,GACL;AACA,UAAM,OAAO;AACb,SAAK,eAAe,IAAI,YAAY,SAAS;AAAA,MAC3C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,aAAa;AACtB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,WAAO,KAAK,aAAa,SAAS,IAAI;AAAA,EACxC;AACF;AAIA,IAAM,eAAN,cAA2B,UAAU;AAAA,EACnC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EACA,WAAW,OAAO;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,aAAa;AACtB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,QAAI,WAAW;AACf,QAAI;AAEJ,UAAM,UAAU,CAAC;AACjB,UAAM,aAAa,KAAK,QAAQ;AAGhC,YAAQ,QAAQ,KAAK,QAAQ,KAAK,SAAS,QAAQ,KAAK,IAAI;AAC1D,iBAAW,QAAQ;AACnB,cAAQ,KAAK,CAAC,OAAO,WAAW,CAAC,CAAC;AAAA,IACpC;AAEA,UAAM,UAAU,CAAC,CAAC,QAAQ;AAE1B,WAAO;AAAA,MACL;AAAA,MACA,OAAO,UAAU,IAAI;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,eAAe,UAAU;AAG/B,IAAM,WAAW;AACjB,IAAM,WAAW;AAKjB,SAAS,WAAW,SAAS,UAAU,CAAC,GAAG;AACzC,SAAO,QAAQ,MAAM,QAAQ,EAAE,IAAI,CAAC,SAAS;AAC3C,QAAI,QAAQ,KACT,KAAK,EACL,MAAM,QAAQ,EACd,OAAO,CAACC,UAASA,SAAQ,CAAC,CAACA,MAAK,KAAK,CAAC;AAEzC,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK,GAAG;AACnD,YAAM,YAAY,MAAM,CAAC;AAGzB,UAAI,QAAQ;AACZ,UAAI,MAAM;AACV,aAAO,CAAC,SAAS,EAAE,MAAM,cAAc;AACrC,cAAM,WAAW,UAAU,GAAG;AAC9B,YAAI,QAAQ,SAAS,aAAa,SAAS;AAC3C,YAAI,OAAO;AACT,kBAAQ,KAAK,IAAI,SAAS,OAAO,OAAO,CAAC;AACzC,kBAAQ;AAAA,QACV;AAAA,MACF;AAEA,UAAI,OAAO;AACT;AAAA,MACF;AAGA,YAAM;AACN,aAAO,EAAE,MAAM,cAAc;AAC3B,cAAM,WAAW,UAAU,GAAG;AAC9B,YAAI,QAAQ,SAAS,cAAc,SAAS;AAC5C,YAAI,OAAO;AACT,kBAAQ,KAAK,IAAI,SAAS,OAAO,OAAO,CAAC;AACzC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT,CAAC;AACH;AAIA,IAAM,gBAAgB,oBAAI,IAAI,CAAC,WAAW,MAAM,aAAa,IAAI,CAAC;AA8BlE,IAAM,iBAAN,MAAqB;AAAA,EACnB,YACE,SACA;AAAA,IACE,kBAAkB,OAAO;AAAA,IACzB,iBAAiB,OAAO;AAAA,IACxB,qBAAqB,OAAO;AAAA,IAC5B,iBAAiB,OAAO;AAAA,IACxB,iBAAiB,OAAO;AAAA,IACxB,WAAW,OAAO;AAAA,IAClB,YAAY,OAAO;AAAA,IACnB,WAAW,OAAO;AAAA,EACpB,IAAI,CAAC,GACL;AACA,SAAK,QAAQ;AACb,SAAK,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,SAAK,UAAU,kBAAkB,UAAU,QAAQ,YAAY;AAC/D,SAAK,QAAQ,WAAW,KAAK,SAAS,KAAK,OAAO;AAAA,EACpD;AAAA,EAEA,OAAO,UAAU,GAAG,SAAS;AAC3B,WAAO,QAAQ;AAAA,EACjB;AAAA,EAEA,SAAS,MAAM;AACb,UAAM,QAAQ,KAAK;AAEnB,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAEA,UAAM,EAAE,gBAAgB,gBAAgB,IAAI,KAAK;AAEjD,WAAO,kBAAkB,OAAO,KAAK,YAAY;AAEjD,QAAI,aAAa;AACjB,QAAI,aAAa,CAAC;AAClB,QAAI,aAAa;AAGjB,aAAS,IAAI,GAAG,OAAO,MAAM,QAAQ,IAAI,MAAM,KAAK,GAAG;AACrD,YAAMC,aAAY,MAAM,CAAC;AAGzB,iBAAW,SAAS;AACpB,mBAAa;AAGb,eAAS,IAAI,GAAG,OAAOA,WAAU,QAAQ,IAAI,MAAM,KAAK,GAAG;AACzD,cAAM,WAAWA,WAAU,CAAC;AAC5B,cAAM,EAAE,SAAS,SAAS,MAAM,IAAI,SAAS,OAAO,IAAI;AAExD,YAAI,SAAS;AACX,wBAAc;AACd,wBAAc;AACd,cAAI,gBAAgB;AAClB,kBAAM,OAAO,SAAS,YAAY;AAClC,gBAAI,cAAc,IAAI,IAAI,GAAG;AAC3B,2BAAa,CAAC,GAAG,YAAY,GAAG,OAAO;AAAA,YACzC,OAAO;AACL,yBAAW,KAAK,OAAO;AAAA,YACzB;AAAA,UACF;AAAA,QACF,OAAO;AACL,uBAAa;AACb,uBAAa;AACb,qBAAW,SAAS;AACpB;AAAA,QACF;AAAA,MACF;AAGA,UAAI,YAAY;AACd,YAAI,SAAS;AAAA,UACX,SAAS;AAAA,UACT,OAAO,aAAa;AAAA,QACtB;AAEA,YAAI,gBAAgB;AAClB,iBAAO,UAAU;AAAA,QACnB;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAGA,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAM,sBAAsB,CAAC;AAE7B,SAAS,YAAY,MAAM;AACzB,sBAAoB,KAAK,GAAG,IAAI;AAClC;AAEA,SAAS,eAAe,SAAS,SAAS;AACxC,WAAS,IAAI,GAAG,MAAM,oBAAoB,QAAQ,IAAI,KAAK,KAAK,GAAG;AACjE,QAAI,gBAAgB,oBAAoB,CAAC;AACzC,QAAI,cAAc,UAAU,SAAS,OAAO,GAAG;AAC7C,aAAO,IAAI,cAAc,SAAS,OAAO;AAAA,IAC3C;AAAA,EACF;AAEA,SAAO,IAAI,YAAY,SAAS,OAAO;AACzC;AAEA,IAAM,kBAAkB;AAAA,EACtB,KAAK;AAAA,EACL,IAAI;AACN;AAEA,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,SAAS;AACX;AAEA,IAAM,eAAe,CAAC,UACpB,CAAC,EAAE,MAAM,gBAAgB,GAAG,KAAK,MAAM,gBAAgB,EAAE;AAE3D,IAAM,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,QAAQ,IAAI;AAE9C,IAAM,SAAS,CAAC,UACd,CAAC,QAAQ,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC,aAAa,KAAK;AAE3D,IAAM,oBAAoB,CAAC,WAAW;AAAA,EACpC,CAAC,gBAAgB,GAAG,GAAG,OAAO,KAAK,KAAK,EAAE,IAAI,CAAC,SAAS;AAAA,IACtD,CAAC,GAAG,GAAG,MAAM,GAAG;AAAA,EAClB,EAAE;AACJ;AAIA,SAAS,MAAM,OAAO,SAAS,EAAE,OAAO,KAAK,IAAI,CAAC,GAAG;AACnD,QAAM,OAAO,CAACC,WAAU;AACtB,QAAI,OAAO,OAAO,KAAKA,MAAK;AAE5B,UAAM,cAAc,OAAOA,MAAK;AAEhC,QAAI,CAAC,eAAe,KAAK,SAAS,KAAK,CAAC,aAAaA,MAAK,GAAG;AAC3D,aAAO,KAAK,kBAAkBA,MAAK,CAAC;AAAA,IACtC;AAEA,QAAI,OAAOA,MAAK,GAAG;AACjB,YAAM,MAAM,cAAcA,OAAM,QAAQ,IAAI,IAAI,KAAK,CAAC;AAEtD,YAAM,UAAU,cAAcA,OAAM,QAAQ,OAAO,IAAIA,OAAM,GAAG;AAEhE,UAAI,CAAC,SAAS,OAAO,GAAG;AACtB,cAAM,IAAI,MAAM,qCAAqC,GAAG,CAAC;AAAA,MAC3D;AAEA,YAAM,MAAM;AAAA,QACV,OAAO,YAAY,GAAG;AAAA,QACtB;AAAA,MACF;AAEA,UAAI,MAAM;AACR,YAAI,WAAW,eAAe,SAAS,OAAO;AAAA,MAChD;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,OAAO;AAAA,MACT,UAAU,CAAC;AAAA,MACX,UAAU,KAAK,CAAC;AAAA,IAClB;AAEA,SAAK,QAAQ,CAAC,QAAQ;AACpB,YAAM,QAAQA,OAAM,GAAG;AAEvB,UAAI,QAAQ,KAAK,GAAG;AAClB,cAAM,QAAQ,CAAC,SAAS;AACtB,eAAK,SAAS,KAAK,KAAK,IAAI,CAAC;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,aAAa,KAAK,GAAG;AACxB,YAAQ,kBAAkB,KAAK;AAAA,EACjC;AAEA,SAAO,KAAK,KAAK;AACnB;AAGA,SAAS,aACP,SACA,EAAE,kBAAkB,OAAO,gBAAgB,GAC3C;AACA,UAAQ,QAAQ,CAAC,WAAW;AAC1B,QAAI,aAAa;AAEjB,WAAO,QAAQ,QAAQ,CAAC,EAAE,KAAK,MAAAP,OAAM,MAAM,MAAM;AAC/C,YAAM,SAAS,MAAM,IAAI,SAAS;AAElC,oBAAc,KAAK;AAAA,QACjB,UAAU,KAAK,SAAS,OAAO,UAAU;AAAA,SACxC,UAAU,MAAM,kBAAkB,IAAIA;AAAA,MACzC;AAAA,IACF,CAAC;AAED,WAAO,QAAQ;AAAA,EACjB,CAAC;AACH;AAEA,SAAS,iBAAiB,QAAQ,MAAM;AACtC,QAAM,UAAU,OAAO;AACvB,OAAK,UAAU,CAAC;AAEhB,MAAI,CAAC,UAAU,OAAO,GAAG;AACvB;AAAA,EACF;AAEA,UAAQ,QAAQ,CAAC,UAAU;AACzB,QAAI,CAAC,UAAU,MAAM,OAAO,KAAK,CAAC,MAAM,QAAQ,QAAQ;AACtD;AAAA,IACF;AAEA,UAAM,EAAE,SAAS,MAAM,IAAI;AAE3B,QAAI,MAAM;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAEA,QAAI,MAAM,KAAK;AACb,UAAI,MAAM,MAAM,IAAI;AAAA,IACtB;AAEA,QAAI,MAAM,MAAM,IAAI;AAClB,UAAI,WAAW,MAAM;AAAA,IACvB;AAEA,SAAK,QAAQ,KAAK,GAAG;AAAA,EACvB,CAAC;AACH;AAEA,SAAS,eAAe,QAAQ,MAAM;AACpC,OAAK,QAAQ,OAAO;AACtB;AAEA,SAAS,OACP,SACA,MACA;AAAA,EACE,iBAAiB,OAAO;AAAA,EACxB,eAAe,OAAO;AACxB,IAAI,CAAC,GACL;AACA,QAAM,eAAe,CAAC;AAEtB,MAAI;AAAgB,iBAAa,KAAK,gBAAgB;AACtD,MAAI;AAAc,iBAAa,KAAK,cAAc;AAElD,SAAO,QAAQ,IAAI,CAAC,WAAW;AAC7B,UAAM,EAAE,IAAI,IAAI;AAEhB,UAAM,OAAO;AAAA,MACX,MAAM,KAAK,GAAG;AAAA,MACd,UAAU;AAAA,IACZ;AAEA,QAAI,aAAa,QAAQ;AACvB,mBAAa,QAAQ,CAAC,gBAAgB;AACpC,oBAAY,QAAQ,IAAI;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAM,OAAN,MAAW;AAAA,EACT,YAAY,MAAM,UAAU,CAAC,GAAG,OAAO;AACrC,SAAK,UAAU,EAAE,GAAG,QAAQ,GAAG,QAAQ;AAEvC,QACE,KAAK,QAAQ,qBACb,OACA;AACA,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AAEA,SAAK,YAAY,IAAI,SAAS,KAAK,QAAQ,IAAI;AAE/C,SAAK,cAAc,MAAM,KAAK;AAAA,EAChC;AAAA,EAEA,cAAc,MAAM,OAAO;AACzB,SAAK,QAAQ;AAEb,QAAI,SAAS,EAAE,iBAAiB,YAAY;AAC1C,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACtC;AAEA,SAAK,WACH,SACA,YAAY,KAAK,QAAQ,MAAM,KAAK,OAAO;AAAA,MACzC,OAAO,KAAK,QAAQ;AAAA,MACpB,iBAAiB,KAAK,QAAQ;AAAA,IAChC,CAAC;AAAA,EACL;AAAA,EAEA,IAAI,KAAK;AACP,QAAI,CAAC,UAAU,GAAG,GAAG;AACnB;AAAA,IACF;AAEA,SAAK,MAAM,KAAK,GAAG;AACnB,SAAK,SAAS,IAAI,GAAG;AAAA,EACvB;AAAA,EAEA,OAAO,YAAY,MAAoB,OAAO;AAC5C,UAAM,UAAU,CAAC;AAEjB,aAAS,IAAI,GAAG,MAAM,KAAK,MAAM,QAAQ,IAAI,KAAK,KAAK,GAAG;AACxD,YAAM,MAAM,KAAK,MAAM,CAAC;AACxB,UAAI,UAAU,KAAK,CAAC,GAAG;AACrB,aAAK,SAAS,CAAC;AACf,aAAK;AACL,eAAO;AAEP,gBAAQ,KAAK,GAAG;AAAA,MAClB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,KAAK;AACZ,SAAK,MAAM,OAAO,KAAK,CAAC;AACxB,SAAK,SAAS,SAAS,GAAG;AAAA,EAC5B;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,OAAO,OAAO,EAAE,QAAQ,GAAG,IAAI,CAAC,GAAG;AACjC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AAET,QAAI,UAAU,SAAS,KAAK,IACxB,SAAS,KAAK,MAAM,CAAC,CAAC,IACpB,KAAK,kBAAkB,KAAK,IAC5B,KAAK,kBAAkB,KAAK,IAC9B,KAAK,eAAe,KAAK;AAE7B,iBAAa,SAAS,EAAE,gBAAgB,CAAC;AAEzC,QAAI,YAAY;AACd,cAAQ,KAAK,MAAM;AAAA,IACrB;AAEA,QAAI,SAAS,KAAK,KAAK,QAAQ,IAAI;AACjC,gBAAU,QAAQ,MAAM,GAAG,KAAK;AAAA,IAClC;AAEA,WAAO,OAAO,SAAS,KAAK,OAAO;AAAA,MACjC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,kBAAkB,OAAO;AACvB,UAAM,WAAW,eAAe,OAAO,KAAK,OAAO;AACnD,UAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,UAAM,UAAU,CAAC;AAGjB,YAAQ,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,KAAK,GAAGA,MAAK,MAAM;AAChD,UAAI,CAAC,UAAU,IAAI,GAAG;AACpB;AAAA,MACF;AAEA,YAAM,EAAE,SAAS,OAAO,QAAQ,IAAI,SAAS,SAAS,IAAI;AAE1D,UAAI,SAAS;AACX,gBAAQ,KAAK;AAAA,UACX,MAAM;AAAA,UACN;AAAA,UACA,SAAS,CAAC,EAAE,OAAO,OAAO,MAAM,MAAAA,OAAM,QAAQ,CAAC;AAAA,QACjD,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,eAAe,OAAO;AAEpB,UAAM,aAAa,MAAM,OAAO,KAAK,OAAO;AAE5C,UAAM,WAAW,CAAC,MAAM,MAAM,QAAQ;AACpC,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,EAAE,OAAO,SAAS,IAAI;AAE5B,cAAM,UAAU,KAAK,aAAa;AAAA,UAChC,KAAK,KAAK,UAAU,IAAI,KAAK;AAAA,UAC7B,OAAO,KAAK,SAAS,uBAAuB,MAAM,KAAK;AAAA,UACvD;AAAA,QACF,CAAC;AAED,YAAI,WAAW,QAAQ,QAAQ;AAC7B,iBAAO;AAAA,YACL;AAAA,cACE;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,eAAO,CAAC;AAAA,MACV;AAEA,YAAM,MAAM,CAAC;AACb,eAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK,GAAG;AAC3D,cAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,cAAM,SAAS,SAAS,OAAO,MAAM,GAAG;AACxC,YAAI,OAAO,QAAQ;AACjB,cAAI,KAAK,GAAG,MAAM;AAAA,QACpB,WAAW,KAAK,aAAa,gBAAgB,KAAK;AAChD,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,KAAK,SAAS;AAC9B,UAAM,YAAY,CAAC;AACnB,UAAM,UAAU,CAAC;AAEjB,YAAQ,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI,MAAM;AACvC,UAAI,UAAU,IAAI,GAAG;AACnB,YAAI,aAAa,SAAS,YAAY,MAAM,GAAG;AAE/C,YAAI,WAAW,QAAQ;AAErB,cAAI,CAAC,UAAU,GAAG,GAAG;AACnB,sBAAU,GAAG,IAAI,EAAE,KAAK,MAAM,SAAS,CAAC,EAAE;AAC1C,oBAAQ,KAAK,UAAU,GAAG,CAAC;AAAA,UAC7B;AACA,qBAAW,QAAQ,CAAC,EAAE,QAAQ,MAAM;AAClC,sBAAU,GAAG,EAAE,QAAQ,KAAK,GAAG,OAAO;AAAA,UACxC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,kBAAkB,OAAO;AACvB,UAAM,WAAW,eAAe,OAAO,KAAK,OAAO;AACnD,UAAM,EAAE,MAAM,QAAQ,IAAI,KAAK;AAC/B,UAAM,UAAU,CAAC;AAGjB,YAAQ,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI,MAAM;AACvC,UAAI,CAAC,UAAU,IAAI,GAAG;AACpB;AAAA,MACF;AAEA,UAAI,UAAU,CAAC;AAGf,WAAK,QAAQ,CAAC,KAAK,aAAa;AAC9B,gBAAQ;AAAA,UACN,GAAG,KAAK,aAAa;AAAA,YACnB;AAAA,YACA,OAAO,KAAK,QAAQ;AAAA,YACpB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,QAAQ,QAAQ;AAClB,gBAAQ,KAAK;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EACA,aAAa,EAAE,KAAK,OAAO,SAAS,GAAG;AACrC,QAAI,CAAC,UAAU,KAAK,GAAG;AACrB,aAAO,CAAC;AAAA,IACV;AAEA,QAAI,UAAU,CAAC;AAEf,QAAI,QAAQ,KAAK,GAAG;AAClB,YAAM,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,KAAK,GAAGA,MAAK,MAAM;AAC9C,YAAI,CAAC,UAAU,IAAI,GAAG;AACpB;AAAA,QACF;AAEA,cAAM,EAAE,SAAS,OAAO,QAAQ,IAAI,SAAS,SAAS,IAAI;AAE1D,YAAI,SAAS;AACX,kBAAQ,KAAK;AAAA,YACX;AAAA,YACA;AAAA,YACA,OAAO;AAAA,YACP;AAAA,YACA,MAAAA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,YAAM,EAAE,GAAG,MAAM,GAAGA,MAAK,IAAI;AAE7B,YAAM,EAAE,SAAS,OAAO,QAAQ,IAAI,SAAS,SAAS,IAAI;AAE1D,UAAI,SAAS;AACX,gBAAQ,KAAK,EAAE,OAAO,KAAK,OAAO,MAAM,MAAAA,OAAM,QAAQ,CAAC;AAAA,MACzD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEA,KAAK,UAAU;AACf,KAAK,cAAc;AACnB,KAAK,aAAa;AAClB,KAAK,SAAS;AAEd;AACE,OAAK,aAAa;AACpB;AAEA;AACE,WAAS,cAAc;AACzB;", "names": ["obj", "path", "norm", "value", "score", "pattern", "result", "item", "searchers", "query"]}